# Developer Role Documentation

## Overview

The `DEVELOPER` role is a special system administration role designed for developers and system administrators who need access to system-level functionality and debugging tools.

## Key Characteristics

### 1. Database-Only Assignment
- **CANNOT** be assigned through the UI
- **CANNOT** be modified through the UI
- Must be assigned directly through database operations
- Protected from accidental modification

### 2. Highest Permission Level
- Role hierarchy level: 4 (highest)
- Has access to all system functionality
- Can access developer-specific pages and tools

## Role Hierarchy

```
DEVELOPER: 4 (highest - system administration)
OWNER: 3
ADMIN: 2
CASHIER: 1 (lowest)
```

## Assigning Developer Role

### Via Database (Recommended)
```sql
-- Assign DEVELOPER role to a user
UPDATE "User" SET role = 'DEVELOPER' WHERE id = 'user_id_here';

-- Or via Prisma Studio/CLI
```

### Via Prisma CLI
```bash
# Connect to your database
npx prisma studio

# Navigate to User table and manually set role to 'DEVELOPER'
```

## Developer-Only Pages

The following pages are accessible ONLY to users with the DEVE<PERSON><PERSON><PERSON> role:

- `/dashboard/developer` - Main developer dashboard
- `/dashboard/developer/system` - System information and health
- `/dashboard/developer/database` - Database administration tools
- `/dashboard/developer/logs` - System logs and debugging
- `/dashboard/developer/users` - User management (all users across system)
- `/dashboard/developer/analytics` - System-wide analytics

## Security Features

### UI Protection
- Role assignment forms will reject DEVELOPER role selection
- Users with DEVELOPER role cannot be modified through UI
- Error messages inform users that DEVELOPER role is database-only

### Access Control
- All developer pages are protected by role-based access control
- Automatic redirection for unauthorized access attempts
- Session-based role verification

## Implementation Details

### Schema Changes
```prisma
enum Role {
  OWNER
  ADMIN
  CASHIER
  DEVELOPER  // Added
}
```

### RBAC Configuration
```typescript
// Role hierarchy
export const roleHierarchy: Record<Role, number> = {
  DEVELOPER: 4, // Highest level
  OWNER: 3,
  ADMIN: 2,
  CASHIER: 1,
};

// Page permissions
export const pagePermissions: Record<string, Role[]> = {
  // ... existing permissions
  "/dashboard/developer": [Role.DEVELOPER],
  "/dashboard/developer/system": [Role.DEVELOPER],
  // ... other developer pages
};
```

### Protection Logic
```typescript
// Prevent UI assignment
if (newRole === Role.DEVELOPER) {
  return { error: "Peran Developer hanya dapat ditetapkan melalui database secara langsung!" };
}

// Prevent UI modification
if (userToUpdate.role === Role.DEVELOPER) {
  return { error: "Pengguna dengan peran Developer tidak dapat diubah melalui UI!" };
}
```

## Best Practices

1. **Limited Assignment**: Only assign DEVELOPER role to trusted system administrators
2. **Regular Audit**: Periodically review users with DEVELOPER role
3. **Secure Access**: Ensure database access is properly secured
4. **Documentation**: Keep track of who has DEVELOPER role and why
5. **Monitoring**: Monitor access to developer-only pages

## Troubleshooting

### Cannot Access Developer Pages
- Verify user has DEVELOPER role in database
- Check session is properly authenticated
- Ensure role-based access control is working

### Role Assignment Issues
- Remember: DEVELOPER role MUST be assigned via database
- UI will always reject DEVELOPER role assignment
- Use database tools or direct SQL queries

### Permission Errors
- DEVELOPER role has highest permissions
- Should have access to all system functionality
- Check RBAC configuration if issues persist

## Migration Notes

When deploying this feature:

1. Run database migration to add DEVELOPER to Role enum
2. Update any existing role-checking logic
3. Test role-based access control
4. Assign DEVELOPER role to appropriate users via database
5. Verify UI protection is working

## Security Considerations

- DEVELOPER role has system-wide access
- Can potentially access all user data
- Should only be assigned to trusted personnel
- Consider additional authentication for developer pages
- Monitor and log developer page access
