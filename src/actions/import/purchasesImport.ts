"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { generateSupplierId, generatePurchaseId } from "@/lib/generate-id";
import { createSystemNotification } from "@/lib/create-system-notification";
import { getNextTransactionNumber } from "@/actions/entities/purchases";
import * as XLSX from "xlsx"; // Mengimpor library XLSX untuk membaca dan menulis file Excel

// --- Interfaces (Definisi Tipe Data) ---

/**
 * @interface ImportSummary
 * @description Mendefinisikan struktur ringkasan hasil impor pembelian.
 * Ini mencakup jumlah data yang berhasil dibuat, error yang terjadi,
 * dan baris yang dilewati.
 */
interface ImportSummary {
  purchasesCreated: number; // Jumlah pembelian yang berhasil dibuat
  suppliersCreated: number; // Jumlah supplier baru yang berhasil dibuat
  warehousesCreated: number; // Jumlah gudang baru yang berhasil dibuat (saat ini selalu 0, bisa dikembangkan)
  errors: string[]; // Daftar pesan error yang terjadi selama proses import
  skippedRows: number; // Jumlah baris yang dilewati karena konflik atau error
  conflictedInvoices: string[]; // Daftar nomor invoice yang berkonflik (sudah ada)
  missingInvoices: number; // JUMLAH BARU: Jumlah transaksi tanpa nomor invoice
  failedTransactions: FailedTransaction[]; // JUMLAH BARU: Detail transaksi yang gagal beserta alasannya
}

/**
 * @interface ImportResult
 * @description Mendefinisikan struktur hasil akhir dari operasi import pembelian.
 * Ini bisa berupa pesan sukses atau error, ringkasan, dan detail konflik/missing invoice.
 */
interface ImportResult {
  success?: string; // Pesan sukses jika import berhasil sebagian atau seluruhnya
  error?: string; // Pesan error jika import gagal
  summary: ImportSummary; // Ringkasan hasil import
  hasConflicts?: boolean; // Flag untuk menunjukkan apakah ada konflik invoice
  conflictDetails?: ConflictDetails; // Detail tentang konflik invoice jika ada
  hasMissingInvoices?: boolean; // JUMLAH BARU: Flag untuk menunjukkan apakah ada transaksi tanpa invoice
  missingInvoiceDetails?: MissingInvoiceDetails; // JUMLAH BARU: Detail tentang transaksi tanpa invoice jika ada
}

/**
 * @interface ConflictDetails
 * @description Mendefinisikan detail tentang konflik invoice yang ditemukan.
 */
interface ConflictDetails {
  duplicateInvoices: string[]; // Daftar invoice yang terdeteksi duplikat di database
  affectedRows: number[]; // Indeks baris (dari file Excel) yang terpengaruh oleh konflik
  validRowsCount: number; // Jumlah baris yang dianggap valid setelah pengecekan konflik
  totalRowsCount: number; // Total jumlah baris yang diproses
}

/**
 * @interface MissingInvoiceDetails
 * @description JUMLAH BARU: Mendefinisikan detail tentang transaksi yang tidak memiliki nomor invoice.
 */
interface MissingInvoiceDetails {
  transactionsWithoutInvoice: number; // Jumlah transaksi yang tidak memiliki nomor invoice
  affectedRows: number[]; // Indeks baris (dari file Excel) yang tidak memiliki nomor invoice
  validRowsCount: number; // Jumlah baris yang dianggap valid (memiliki invoice)
  totalRowsCount: number; // Total jumlah baris yang diproses
}

/**
 * @interface FailedTransaction
 * @description JUMLAH BARU: Mendefinisikan detail tentang transaksi yang gagal diimpor.
 */
interface FailedTransaction {
  rowIndex: number; // Nomor baris di Excel tempat transaksi gagal
  productName: string; // Nama produk yang terlibat dalam transaksi gagal
  reason: string; // Alasan kegagalan transaksi
  invoiceRef?: string; // Nomor invoice yang terkait (opsional)
}

/**
 * @interface ProcessedRow
 * @description Mendefinisikan struktur data baris yang telah diproses dari Excel,
 * termasuk data asli, indeks baris, status validasi, dan pesan error.
 */
interface ProcessedRow {
  data: any; // Data mentah dari baris Excel
  rowIndex: number; // Indeks baris asli dari file Excel
  invoiceRef?: string; // Nomor invoice yang diekstrak dari baris
  isValid: boolean; // Status validasi baris (true jika valid, false jika tidak)
  errorMessage?: string; // Pesan error jika baris tidak valid
}

// --- Utility Functions (Fungsi Pembantu) ---

/**
 * @function sanitizeString
 * @description Membersihkan dan memvalidasi input string.
 * Mengembalikan string kosong jika input null/undefined/kosong,
 * dan memotong string hingga 255 karakter.
 * @param {any} value - Nilai yang akan dibersihkan.
 * @returns {string} - String yang sudah dibersihkan.
 */
const sanitizeString = (value: any): string => {
  if (value === null || value === undefined || value === "") return "";
  return String(value).trim().substring(0, 255);
};

/**
 * @function sanitizeNumber
 * @description Membersihkan dan memvalidasi input angka.
 * Mengembalikan 0 jika input null/undefined/kosong atau tidak valid (NaN),
 * dan memastikan angka tidak negatif.
 * @param {any} value - Nilai yang akan dibersihkan.
 * @returns {number} - Angka yang sudah dibersihkan.
 */
const sanitizeNumber = (value: any): number => {
  if (value === null || value === undefined || value === "") return 0;
  const num = Number(value);
  return isNaN(num) ? 0 : Math.max(0, num);
};

/**
 * @function sanitizeDate
 * @description Membersihkan dan memvalidasi input tanggal.
 * Mengembalikan tanggal saat ini jika input null/undefined/kosong/tidak valid.
 * Menangani nomor seri tanggal Excel, string tanggal, dan objek Date.
 * @param {any} value - Nilai yang akan dibersihkan.
 * @returns {Date} - Objek Date yang sudah dibersihkan.
 */
const sanitizeDate = (value: any): Date => {
  if (value === null || value === undefined || value === "") {
    return new Date();
  }

  // Tangani nomor seri tanggal Excel (jumlah hari sejak 1900-01-01)
  if (typeof value === "number") {
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(
      excelEpoch.getTime() + (value - 1) * 24 * 60 * 60 * 1000
    );
    return isNaN(date.getTime()) ? new Date() : date;
  }

  // Tangani string tanggal
  if (typeof value === "string") {
    const date = new Date(value);
    return isNaN(date.getTime()) ? new Date() : date;
  }

  // Tangani objek Date
  if (value instanceof Date) {
    return isNaN(value.getTime()) ? new Date() : value;
  }

  return new Date();
};

/**
 * @function handleMergedCells
 * @description Menangani sel yang digabungkan (merged cells) di Excel dengan mengisi nilai ke bawah
 * dari sel yang digabungkan. Fungsi ini juga berupaya mengelompokkan
 * beberapa baris produk di bawah invoice yang sama.
 * @param {any[][]} data - Data mentah dari sheet Excel (array of arrays).
 * @param {string[]} headers - Daftar header kolom.
 * @returns {any[][]} - Data yang sudah diproses dengan sel yang digabungkan terisi.
 */
const handleMergedCells = (data: any[][], headers: string[]): any[][] => {
  console.log("[IMPORT] Processing merged cells and grouping by invoice...");

  const processedData = [...data];

  // Kolom yang umumnya digabungkan di Excel
  const mergeableColumns = [
    "No. Transaksi",
    "Tanggal Pembelian",
    "Nama Supplier",
    "Nomor Telepon",
    "Email Supplier",
    "No. Invoice",
    "Status Pembayaran",
    "Total Pembelian",
  ];
  const lastValues: { [key: string]: any } = {}; // Menyimpan nilai terakhir yang tidak kosong untuk setiap kolom

  // Langkah 1: Isi nilai dari sel yang digabungkan ke bawah
  for (let rowIndex = 0; rowIndex < processedData.length; rowIndex++) {
    const row = processedData[rowIndex];

    headers.forEach((header, colIndex) => {
      if (mergeableColumns.includes(header)) {
        const cellValue = row[colIndex];

        // Jika sel kosong/null dan ada nilai sebelumnya, gunakan nilai sebelumnya
        if (
          (cellValue === null || cellValue === undefined || cellValue === "") &&
          lastValues[header]
        ) {
          row[colIndex] = lastValues[header];
          console.log(
            `[IMPORT] Filled merged cell at row ${rowIndex + 1}, column "${header}" with value: ${lastValues[header]}`
          );
        }
        // Jika sel memiliki nilai, perbarui pelacakan nilai terakhir
        else if (
          cellValue !== null &&
          cellValue !== undefined &&
          cellValue !== ""
        ) {
          lastValues[header] = cellValue;
        }
      }
    });
  }

  // Langkah 2: Kelompokkan baris berdasarkan invoice dan gabungkan informasi produk
  const groupedData: any[][] = [];
  const invoiceGroups: { [key: string]: any[][] } = {};

  // Kelompokkan baris berdasarkan nomor invoice (atau buat identifikasi unik jika tanpa invoice)
  for (let rowIndex = 0; rowIndex < processedData.length; rowIndex++) {
    const row = processedData[rowIndex];
    const invoiceColIndex = headers.indexOf("No. Invoice");
    const transactionColIndex = headers.indexOf("No. Transaksi");
    const dateColIndex = headers.indexOf("Tanggal Pembelian");
    const supplierColIndex = headers.indexOf("Nama Supplier");

    let groupKey = "";

    // Gunakan nomor invoice sebagai kunci grup jika tersedia
    if (invoiceColIndex >= 0 && row[invoiceColIndex]) {
      groupKey = String(row[invoiceColIndex]).trim();
    }
    // Jika tidak ada invoice, buat kunci unik dari nomor transaksi + tanggal + supplier
    else {
      const transactionNum =
        transactionColIndex >= 0
          ? String(row[transactionColIndex] || "").trim()
          : "";
      const date =
        dateColIndex >= 0 ? String(row[dateColIndex] || "").trim() : "";
      const supplier =
        supplierColIndex >= 0 ? String(row[supplierColIndex] || "").trim() : "";
      groupKey = `${transactionNum}-${date}-${supplier}-${rowIndex}`;
    }

    if (!invoiceGroups[groupKey]) {
      invoiceGroups[groupKey] = [];
    }
    invoiceGroups[groupKey].push(row);
  }

  console.log(
    `[IMPORT] Found ${Object.keys(invoiceGroups).length} invoice groups from ${processedData.length} rows`
  );

  // Langkah 3: Untuk setiap grup invoice, buat satu baris perwakilan.
  // Jika beberapa produk dalam invoice yang sama, kita akan tetap memisahkannya tetapi mencatat pengelompokannya.
  Object.entries(invoiceGroups).forEach(([groupKey, rows]) => {
    if (rows.length === 1) {
      // Invoice produk tunggal - tambahkan apa adanya
      groupedData.push(rows[0]);
    } else {
      // Beberapa produk dalam invoice yang sama - pisahkan tetapi catat
      console.log(
        `[IMPORT] Invoice group "${groupKey}" has ${rows.length} products:`
      );
      rows.forEach((row, index) => {
        const productNameColIndex = headers.indexOf("Nama Produk");
        const productName =
          productNameColIndex >= 0
            ? row[productNameColIndex]
            : `Product ${index + 1}`;
        console.log(`  - Product ${index + 1}: ${productName}`);
        groupedData.push(row);
      });
    }
  });

  console.log(
    `[IMPORT] After processing merged cells and grouping: ${groupedData.length} data rows`
  );
  return groupedData;
};

/**
 * @function validateTransactionLimit
 * @description Melakukan validasi batas jumlah transaksi,
 * dengan menghitung transaksi unik (berdasarkan invoice atau kombinasi lainnya)
 * bukan hanya jumlah baris. Batas maksimal 100 transaksi.
 * @param {any[]} purchaseData - Data pembelian yang akan divalidasi.
 * @returns {{ isValid: boolean; errorMessage?: string; uniqueTransactions: number }} - Hasil validasi.
 */
const validateTransactionLimit = (
  purchaseData: any[]
): { isValid: boolean; errorMessage?: string; uniqueTransactions: number } => {
  // Hitung transaksi unik (invoice) alih-alih total baris
  const uniqueInvoices = new Set<string>();
  const transactionsWithoutInvoice = new Set<string>();

  purchaseData.forEach((row) => {
    const invoiceRef = sanitizeString(row["No. Invoice"]);
    const purchaseDate = sanitizeDate(row["Tanggal Pembelian"]);

    if (invoiceRef) {
      uniqueInvoices.add(invoiceRef);
    } else {
      // Untuk baris tanpa invoice, kelompokkan berdasarkan tanggal + supplier untuk estimasi transaksi unik
      const supplierName =
        sanitizeString(row["Nama Supplier"]) || "NO_SUPPLIER";
      const transactionNum =
        sanitizeString(row["No. Transaksi"]) || "NO_TRANSACTION";
      const dateKey = purchaseDate.toISOString().split("T")[0];
      transactionsWithoutInvoice.add(
        `${dateKey}-${supplierName}-${transactionNum}`
      );
    }
  });

  const totalUniqueTransactions =
    uniqueInvoices.size + transactionsWithoutInvoice.size;

  console.log(`[IMPORT] Transaction analysis:`);
  console.log(`  - Unique invoices: ${uniqueInvoices.size}`);
  console.log(
    `  - Transactions without invoice: ${transactionsWithoutInvoice.size}`
  );
  console.log(`  - Total unique transactions: ${totalUniqueTransactions}`);
  console.log(`  - Total data rows: ${purchaseData.length}`);

  if (totalUniqueTransactions > 100) {
    return {
      isValid: false,
      errorMessage: `Terlalu banyak transaksi. Maksimal 100 transaksi per import. Ditemukan ${totalUniqueTransactions} transaksi dari ${purchaseData.length} baris data.`,
      uniqueTransactions: totalUniqueTransactions,
    };
  }

  return {
    isValid: true,
    uniqueTransactions: totalUniqueTransactions,
  };
};

/**
 * @function checkMissingInvoices
 * @description JUMLAH BARU: Memeriksa baris-baris pembelian untuk menemukan transaksi yang tidak memiliki nomor invoice.
 * @param {ProcessedRow[]} purchaseRows - Array dari baris yang sudah diproses.
 * @returns {Promise<MissingInvoiceDetails>} - Detail tentang transaksi tanpa invoice.
 */
const checkMissingInvoices = async (
  purchaseRows: ProcessedRow[]
): Promise<MissingInvoiceDetails> => {
  console.log("[IMPORT] Checking for missing invoice numbers...");

  const transactionsWithoutInvoice = purchaseRows.filter((row) => {
    const invoiceRef = row.invoiceRef;
    // Baris dianggap tidak memiliki invoice jika invoiceRef kosong atau hanya spasi
    return !invoiceRef || invoiceRef.trim() === "";
  });

  // Dapatkan indeks baris asli dari transaksi yang tidak memiliki invoice
  const affectedRows = transactionsWithoutInvoice.map((row, index) =>
    purchaseRows.indexOf(row)
  );

  const validRowsCount =
    purchaseRows.length - transactionsWithoutInvoice.length;

  console.log(
    `[IMPORT] Missing invoice check results: ${transactionsWithoutInvoice.length} transactions without invoice numbers, ${validRowsCount} transactions with valid invoices`
  );

  return {
    transactionsWithoutInvoice: transactionsWithoutInvoice.length,
    affectedRows,
    validRowsCount,
    totalRowsCount: purchaseRows.length,
  };
};

/**
 * @function checkInvoiceConflicts
 * @description Memeriksa konflik nomor invoice dengan data pembelian yang sudah ada di database.
 * Menggunakan pencocokan case-sensitive.
 * @param {ProcessedRow[]} purchaseRows - Array dari baris yang sudah diproses.
 * @param {string} effectiveUserId - ID pengguna yang sedang login.
 * @returns {Promise<ConflictDetails>} - Detail tentang konflik invoice.
 */
const checkInvoiceConflicts = async (
  purchaseRows: ProcessedRow[],
  effectiveUserId: string
): Promise<ConflictDetails> => {
  console.log("[IMPORT] Checking for invoice conflicts...");

  // Extract unique invoice references from import data
  const invoiceRefs = purchaseRows
    .map((row) => row.invoiceRef)
    .filter(
      (ref): ref is string =>
        ref !== null && ref !== undefined && ref.trim() !== ""
    );

  const uniqueInvoiceRefs = [...new Set(invoiceRefs)];

  if (uniqueInvoiceRefs.length === 0) {
    console.log(
      "[IMPORT] No valid invoice references found, skipping conflict check"
    );
    return {
      duplicateInvoices: [],
      affectedRows: [],
      validRowsCount: purchaseRows.length,
      totalRowsCount: purchaseRows.length,
    };
  }

  // Check against existing purchases for this specific user
  const existingPurchases = await db.purchase.findMany({
    where: {
      userId: effectiveUserId,
      invoiceRef: {
        in: uniqueInvoiceRefs,
      },
    },
    select: {
      invoiceRef: true,
    },
  });

  const existingInvoiceRefs = new Set(
    existingPurchases
      .map((p) => p.invoiceRef)
      .filter(
        (ref): ref is string =>
          ref !== null && ref !== undefined && ref.trim() !== ""
      )
  );

  console.log(
    `[IMPORT] Found ${existingInvoiceRefs.size} existing invoices that conflict`
  );

  // Find rows with conflicting invoice references
  const duplicateInvoices = Array.from(existingInvoiceRefs);
  const affectedRows = purchaseRows
    .map((row, index) => ({ ...row, originalIndex: index }))
    .filter((row) => {
      return (
        row.invoiceRef &&
        row.invoiceRef.trim() !== "" &&
        existingInvoiceRefs.has(row.invoiceRef)
      );
    })
    .map((row) => row.originalIndex);

  const validRowsCount = purchaseRows.length - affectedRows.length;

  console.log(
    `[IMPORT] Conflict check results: ${affectedRows.length} rows have conflicts, ${validRowsCount} rows are valid`
  );

  return {
    duplicateInvoices,
    affectedRows,
    validRowsCount,
    totalRowsCount: purchaseRows.length,
  };
};

/**
 * @function processAndValidatePurchaseData
 * @description Memproses dan memvalidasi setiap baris data pembelian dari Excel.
 * Termasuk validasi wajib untuk Nama Produk, Quantity, Harga Beli,
 * dan Nomor Invoice (sekarang wajib). Juga memeriksa ketersediaan produk
 * dengan pencocokan case-sensitive.
 * @param {any[]} purchaseData - Array dari objek data pembelian dari Excel.
 * @param {string} effectiveUserId - ID pengguna yang sedang login.
 * @param {Map<string, string>} existingProductMap - Map produk yang sudah ada (Nama Produk -> ID Produk, case-sensitive).
 * @returns {Promise<ProcessedRow[]>} - Array dari baris yang sudah diproses dan divalidasi.
 */
const processAndValidatePurchaseData = async (
  purchaseData: any[],
  effectiveUserId: string,
  existingProductMap: Map<string, string>
): Promise<ProcessedRow[]> => {
  console.log("[IMPORT] Processing and validating purchase data...");

  const processedRows: ProcessedRow[] = [];

  for (const [index, row] of purchaseData.entries()) {
    const processedRow: ProcessedRow = {
      data: row,
      rowIndex: row._rowIndex, // Menggunakan indeks baris asli dari Excel
      isValid: true,
      errorMessage: undefined,
    };

    try {
      // Ekstrak data dasar dan bersihkan
      const productName = sanitizeString(row["Nama Produk"]);
      const quantity = sanitizeNumber(row["Quantity"]);
      const costPrice = sanitizeNumber(row["Harga Beli"]);
      const invoiceRef = sanitizeString(row["No. Invoice"]);

      // Set referensi invoice untuk pengecekan konflik
      processedRow.invoiceRef = invoiceRef || undefined;

      // VALIDASI YANG DITINGKATKAN: Validasi field wajib termasuk invoice
      if (!productName) {
        processedRow.isValid = false;
        processedRow.errorMessage = "Nama Produk tidak boleh kosong";
      } else if (!invoiceRef || invoiceRef.trim() === "") {
        // BARU: Nomor Invoice sekarang wajib
        processedRow.isValid = false;
        processedRow.errorMessage =
          "No. Invoice tidak boleh kosong dan harus diisi";
      } else if (quantity <= 0) {
        processedRow.isValid = false;
        processedRow.errorMessage = "Quantity harus lebih dari 0";
      } else if (costPrice <= 0) {
        processedRow.isValid = false;
        processedRow.errorMessage = "Harga Beli harus lebih dari 0";
      } else {
        // Pencocokan produk case-sensitive
        if (!existingProductMap.has(productName)) {
          processedRow.isValid = false;
          processedRow.errorMessage = `Produk "${productName}" tidak ditemukan (case-sensitive)`;

          // Sarankan produk serupa jika ada (pencocokan case-insensitive)
          const similarProducts = Array.from(existingProductMap.keys()).filter(
            (name) => name.toLowerCase() === productName.toLowerCase()
          );

          if (similarProducts.length > 0) {
            processedRow.errorMessage += `. Mungkin maksud Anda: ${similarProducts.join(", ")}`;
          }
        }
      }
    } catch (error) {
      console.error(
        `[IMPORT] Error validating row ${processedRow.rowIndex}:`,
        error
      );
      processedRow.isValid = false;
      processedRow.errorMessage = `Error validasi: ${error instanceof Error ? error.message : "Unknown error"}`;
    }

    processedRows.push(processedRow);
  }

  const validCount = processedRows.filter((row) => row.isValid).length;
  console.log(
    `[IMPORT] Validation complete: ${validCount}/${processedRows.length} rows valid`
  );

  return processedRows;
};

/**
 * @function importPurchases
 * @description Fungsi utama untuk mengimpor data pembelian dari file Excel.
 * Menangani parsing file, validasi header, pengisian merged cells,
 * validasi batas transaksi, pengecekan konflik invoice, penanganan
 * transaksi tanpa invoice, dan proses penyimpanan ke database.
 * Ini adalah fungsi yang ditingkatkan dengan deteksi konflik dan
 * penanganan transaksi tanpa invoice.
 * @param {ArrayBuffer} arrayBuffer - Data file Excel dalam format ArrayBuffer.
 * @param {boolean} skipConflicts - Flag untuk melewati baris yang berkonflik invoice jika true.
 * @param {boolean} skipMissingInvoices - BARU: Flag untuk melewati baris tanpa invoice jika true.
 * @returns {Promise<ImportResult>} - Objek ImportResult yang berisi hasil import.
 */
export const importPurchases = async (
  arrayBuffer: ArrayBuffer,
  skipConflicts: boolean = false, // Default: jangan lewati konflik
  skipMissingInvoices: boolean = false // BARU: Default: jangan lewati missing invoice
): Promise<ImportResult> => {
  try {
    console.log(
      "[IMPORT] Starting enhanced purchase import process with invoice validation"
    );

    // Dapatkan ID pengguna yang sedang login
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return {
        error: "Tidak terautentikasi",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["User tidak terautentikasi"],
          skippedRows: 0,
          conflictedInvoices: [],
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    // Parsing file Excel
    const workbook = XLSX.read(arrayBuffer, { type: "array" });
    const sheetName = workbook.SheetNames[0]; // Ambil nama sheet pertama
    const worksheet = workbook.Sheets[sheetName]; // Dapatkan worksheet pertama
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 }); // Konversi ke JSON (array of arrays)

    console.log(`[IMPORT] Parsed ${data.length} rows from Excel`);

    if (data.length < 2) {
      return {
        error: "File Excel kosong atau tidak memiliki data",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["File tidak memiliki data yang valid"],
          skippedRows: 0,
          conflictedInvoices: [],
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    // Cari baris header dan ekstrak header - VERSI YANG DITINGKATKAN
    let headerRowIndex = -1;
    let headers: string[] = [];

    // Cari baris yang mengandung header yang diharapkan (maksimal 10 baris pertama)
    const expectedHeaders = [
      "No. Transaksi",
      "Tanggal Pembelian",
      "No. Invoice",
      "Nama Supplier",
    ];

    for (let i = 0; i < Math.min(10, data.length); i++) {
      const row = data[i] as string[];
      if (row && Array.isArray(row)) {
        // Konversi semua sel menjadi string dan cek header kunci
        const rowStrings = row.map((cell) =>
          cell ? cell.toString().trim() : ""
        );

        // Cek apakah baris ini mengandung setidaknya 2 dari header yang diharapkan
        const foundHeaders = expectedHeaders.filter((expectedHeader) =>
          rowStrings.some(
            (cell) =>
              cell.toLowerCase().includes(expectedHeader.toLowerCase()) ||
              expectedHeader.toLowerCase().includes(cell.toLowerCase())
          )
        );

        if (foundHeaders.length >= 2) {
          headerRowIndex = i;
          // Bersihkan header - hapus sel kosong dan trim
          headers = rowStrings.filter((cell) => cell !== "");
          console.log(`[IMPORT] Found headers at row ${i + 1}:`, headers);
          break;
        }
      }
    }

    if (headerRowIndex === -1 || headers.length === 0) {
      return {
        error:
          "Header tidak ditemukan. Pastikan file menggunakan template yang benar dengan header: No. Transaksi, Tanggal Pembelian, No. Invoice, Nama Supplier, dll.",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["Header tidak valid atau tidak ditemukan"],
          skippedRows: 0,
          conflictedInvoices: [],
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    console.log(
      `[IMPORT] Headers found at row ${headerRowIndex + 1}:`,
      headers
    );

    // Ekstrak baris data HANYA dari baris setelah header
    let dataRows = data.slice(headerRowIndex + 1) as any[][];

    // Filter baris yang sepenuhnya kosong terlebih dahulu
    dataRows = dataRows.filter((row: any[]) => {
      if (!Array.isArray(row)) return false;

      // Cek apakah baris memiliki data yang tidak kosong
      return row.some(
        (cell) =>
          cell !== null && cell !== undefined && cell.toString().trim() !== ""
      );
    });

    console.log(
      `[IMPORT] Found ${dataRows.length} non-empty data rows after header row ${headerRowIndex + 1}`
    );

    // Tangani sel yang digabungkan (merged cells)
    dataRows = handleMergedCells(dataRows, headers);

    // Konversi ke objek dan tambahkan indeks baris (nomor baris Excel)
    const purchaseData = dataRows
      .map((row: any[], index: number) => {
        const obj: any = {};
        headers.forEach((header, i) => {
          obj[header] = row[i];
        });
        // Hitung nomor baris Excel aktual: baris header + 1 + indeks saat ini + 1
        obj._rowIndex = headerRowIndex + index + 2;
        return obj;
      })
      .filter((row) => {
        // Filter tambahan untuk baris yang memiliki setidaknya data yang berarti
        const hasProductName =
          row["Nama Produk"] && String(row["Nama Produk"]).trim() !== "";
        const hasQuantity =
          row["Quantity"] &&
          !isNaN(Number(row["Quantity"])) &&
          Number(row["Quantity"]) > 0;
        const hasPrice =
          row["Harga Beli"] &&
          !isNaN(Number(row["Harga Beli"])) &&
          Number(row["Harga Beli"]) > 0;

        // Baris yang valid harus memiliki setidaknya nama produk dan kuantitas atau harga
        return hasProductName && (hasQuantity || hasPrice);
      });

    console.log(
      `[IMPORT] Extracted ${purchaseData.length} valid data rows after processing`
    );

    if (purchaseData.length === 0) {
      return {
        error: "Tidak ada data pembelian yang valid ditemukan",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: ["Tidak ada baris data yang ditemukan"],
          skippedRows: 0,
          conflictedInvoices: [],
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    // Validasi batas transaksi menggunakan metode yang ditingkatkan
    const validationResult = validateTransactionLimit(purchaseData);

    if (!validationResult.isValid) {
      return {
        error: validationResult.errorMessage,
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: [validationResult.errorMessage || "Validation failed"],
          skippedRows: 0,
          conflictedInvoices: [],
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    console.log(
      `[IMPORT] Validated ${validationResult.uniqueTransactions} unique transactions from ${purchaseData.length} data rows`
    );

    // Dapatkan produk yang sudah ada dengan pencocokan case-sensitive
    console.log(
      "[IMPORT] Fetching existing products for case-sensitive matching..."
    );
    const existingProducts = await db.product.findMany({
      where: {
        userId: effectiveUserId,
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Buat map produk case-sensitive
    const existingProductMap = new Map(
      existingProducts.map((p) => [p.name, p.id]) // Pencocokan case yang persis
    );

    console.log(
      `[IMPORT] Loaded ${existingProductMap.size} existing products for matching`
    );

    // Proses dan validasi semua baris
    const processedRows = await processAndValidatePurchaseData(
      purchaseData,
      effectiveUserId,
      existingProductMap
    );

    // BARU: Periksa transaksi tanpa nomor invoice TERLEBIH DAHULU
    const missingInvoiceDetails = await checkMissingInvoices(processedRows);

    // Jika ada transaksi tanpa invoice dan kita belum diminta untuk melewatinya, kembalikan info missing invoice
    if (
      missingInvoiceDetails.transactionsWithoutInvoice > 0 &&
      !skipMissingInvoices
    ) {
      console.log(
        `[IMPORT] Found missing invoices, returning missing invoice details for user decision`
      );
      return {
        hasMissingInvoices: true,
        missingInvoiceDetails,
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: [],
          skippedRows: missingInvoiceDetails.transactionsWithoutInvoice,
          conflictedInvoices: [],
          missingInvoices: missingInvoiceDetails.transactionsWithoutInvoice,
          failedTransactions: [],
        },
      };
    }

    // Periksa konflik invoice
    const conflictDetails = await checkInvoiceConflicts(
      processedRows,
      effectiveUserId
    );

    // Jika ada konflik dan kita belum diminta untuk melewatinya, kembalikan info konflik
    if (conflictDetails.duplicateInvoices.length > 0 && !skipConflicts) {
      console.log(
        `[IMPORT] Found conflicts, returning conflict details for user decision`
      );
      return {
        hasConflicts: true,
        conflictDetails,
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: [],
          skippedRows: conflictDetails.affectedRows.length,
          conflictedInvoices: conflictDetails.duplicateInvoices,
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    // Filter baris yang tidak valid, baris yang berkonflik, dan baris tanpa invoice
    let validRows = processedRows.filter((row) => row.isValid);

    if (skipConflicts && conflictDetails.duplicateInvoices.length > 0) {
      console.log("[IMPORT] Skipping conflicted invoice rows as requested");
      validRows = validRows.filter(
        (row) =>
          !row.invoiceRef || // Tetap sertakan baris tanpa invoice jika validasinya sudah lolos
          !conflictDetails.duplicateInvoices.includes(row.invoiceRef)
      );
    }

    if (
      skipMissingInvoices &&
      missingInvoiceDetails.transactionsWithoutInvoice > 0
    ) {
      console.log("[IMPORT] Skipping rows with missing invoices as requested");
      validRows = validRows.filter(
        (row) => row.invoiceRef && row.invoiceRef.trim() !== ""
      );
    }

    // Identifikasi baris yang tidak valid, dilewati karena konflik, atau dilewati karena missing invoice
    const invalidRows = processedRows.filter((row) => !row.isValid);
    const skippedConflictRows = skipConflicts
      ? processedRows.filter(
          (row) =>
            row.invoiceRef &&
            conflictDetails.duplicateInvoices.includes(row.invoiceRef)
        )
      : [];
    const skippedMissingInvoiceRows = skipMissingInvoices
      ? processedRows.filter(
          (row) => !row.invoiceRef || row.invoiceRef.trim() === ""
        )
      : [];

    console.log(
      `[IMPORT] Processing: ${validRows.length} valid rows, ${invalidRows.length} invalid rows, ${skippedConflictRows.length} skipped due to conflicts, ${skippedMissingInvoiceRows.length} skipped due to missing invoices`
    );

    // Jika tidak ada baris yang valid untuk diimpor setelah semua filter
    if (validRows.length === 0) {
      const allErrors = [
        ...invalidRows.map(
          (row) => `Baris ${row.rowIndex}: ${row.errorMessage}`
        ),
        ...skippedConflictRows.map(
          (row) =>
            `Baris ${row.rowIndex}: Invoice "${row.invoiceRef}" sudah ada`
        ),
        ...skippedMissingInvoiceRows.map(
          (row) => `Baris ${row.rowIndex}: Tidak ada nomor invoice`
        ),
      ];

      const failedTransactions: FailedTransaction[] = [
        ...invalidRows.map((row) => ({
          rowIndex: row.rowIndex,
          productName: sanitizeString(row.data["Nama Produk"]),
          reason: row.errorMessage || "Error tidak diketahui",
          invoiceRef: row.invoiceRef,
        })),
        ...skippedConflictRows.map((row) => ({
          rowIndex: row.rowIndex,
          productName: sanitizeString(row.data["Nama Produk"]),
          reason: `Invoice "${row.invoiceRef}" sudah ada dalam sistem`,
          invoiceRef: row.invoiceRef,
        })),
        ...skippedMissingInvoiceRows.map((row) => ({
          rowIndex: row.rowIndex,
          productName: sanitizeString(row.data["Nama Produk"]),
          reason: "Nomor invoice tidak ada atau kosong",
          invoiceRef: row.invoiceRef,
        })),
      ];

      return {
        error: "Tidak ada data valid untuk diimpor",
        summary: {
          purchasesCreated: 0,
          suppliersCreated: 0,
          warehousesCreated: 0,
          errors: allErrors,
          skippedRows:
            invalidRows.length +
            skippedConflictRows.length +
            skippedMissingInvoiceRows.length,
          conflictedInvoices: conflictDetails.duplicateInvoices,
          missingInvoices: skippedMissingInvoiceRows.length,
          failedTransactions,
        },
      };
    }

    // Proses baris yang valid
    let totalPurchasesCreated = 0;
    let totalSuppliersCreated = 0;
    const processErrors: string[] = [];
    const failedTransactions: FailedTransaction[] = [];

    console.log(`[IMPORT] Starting to process ${validRows.length} valid rows`);

    // Proses setiap baris yang valid secara individual dalam sebuah transaksi database
    for (const processedRow of validRows) {
      try {
        const result = await db.$transaction(async (tx) => {
          const row = processedRow.data;

          // Ekstrak dan bersihkan data (kita tahu ini valid dari validasi sebelumnya)
          const purchaseDate = sanitizeDate(row["Tanggal Pembelian"]);
          const productName = sanitizeString(row["Nama Produk"]);
          const quantity = sanitizeNumber(row["Quantity"]);
          const costPrice = sanitizeNumber(row["Harga Beli"]);
          const supplierName = sanitizeString(row["Nama Supplier"]);
          const supplierPhone = sanitizeString(row["Nomor Telepon"]);
          const supplierEmail = sanitizeString(row["Email Supplier"]);
          const discountPercentage = sanitizeNumber(row["Diskon (%)"]);
          const discountAmount = sanitizeNumber(row["Diskon (Rp)"]);
          const taxPercentage = sanitizeNumber(row["PPN (%)"]);
          const statusValue = sanitizeString(row["Status Pembayaran"]);
          const memo = sanitizeString(row["Memo"]);
          const invoiceRef = sanitizeString(row["No. Invoice"]);
          const unit = sanitizeString(row["Satuan"]) || "Pcs";

          // Dapatkan ID produk dari map yang sudah divalidasi sebelumnya (case-sensitive)
          const productId = existingProductMap.get(productName);
          if (!productId) {
            // Ini seharusnya tidak terjadi jika validasi sebelumnya sudah benar
            throw new Error(
              `Produk "${productName}" tidak ditemukan (case-sensitive)`
            );
          }

          // Tangani pembuatan/pencarian supplier
          let supplierId: string | null = null;
          let suppliersCreated = 0;

          if (supplierName) {
            let supplier = await tx.supplier.findFirst({
              where: {
                name: supplierName,
                userId: effectiveUserId,
              },
            });

            if (!supplier) {
              const newSupplierId = await generateSupplierId();
              supplier = await tx.supplier.create({
                data: {
                  id: newSupplierId,
                  name: supplierName,
                  phone: supplierPhone || null,
                  email: supplierEmail || null,
                  userId: effectiveUserId,
                },
              });
              suppliersCreated = 1;
              console.log(`[IMPORT] Created new supplier: ${supplierName}`);
            }
            supplierId = supplier.id;
          }

          // Hitung total
          const subtotal = quantity * costPrice;
          const finalDiscountAmount =
            discountAmount > 0
              ? discountAmount
              : (subtotal * discountPercentage) / 100;
          const afterDiscount = subtotal - finalDiscountAmount;
          const taxAmount = (afterDiscount * taxPercentage) / 100;
          const totalAmount = afterDiscount + taxAmount;

          // Parsing status pembayaran
          const status = statusValue === "Lunas" ? "LUNAS" : "BELUM_LUNAS";

          // Hasilkan ID pembelian baru dan nomor transaksi
          const purchaseId = await generatePurchaseId(effectiveUserId);
          const transactionNumberResult = await getNextTransactionNumber(
            "TRX",
            purchaseDate
          );
          const transactionNumber = transactionNumberResult.success
            ? transactionNumberResult.nextNumber
            : `BELI-${Date.now()}`; // Fallback jika gagal generate

          // Buat entri pembelian
          try {
            const purchase = await tx.purchase.create({
              data: {
                id: purchaseId,
                purchaseDate,
                totalAmount,
                transactionNumber,
                invoiceRef: invoiceRef || null,
                memo: memo || null,
                status: status as any,
                userId: effectiveUserId,
                supplierId,
                isDraft: false,
              },
            });

            console.log(
              `[IMPORT] Created purchase: ${purchase.transactionNumber} for product: ${productName}`
            );

            // Buat item pembelian
            await tx.purchaseItem.create({
              data: {
                quantity,
                costAtPurchase: costPrice,
                unit,
                discountPercentage:
                  discountPercentage > 0 ? discountPercentage : null,
                discountAmount:
                  finalDiscountAmount > 0 ? finalDiscountAmount : null,
                tax: taxPercentage > 0 ? `${taxPercentage}%` : null,
                purchaseId: purchase.id,
                productId,
              },
            });

            // Perbarui stok produk
            await tx.product.update({
              where: { id: productId },
              data: {
                stock: {
                  increment: quantity, // Tambahkan stok
                },
              },
            });

            return {
              purchasesCreated: 1,
              suppliersCreated,
            };
          } catch (dbError) {
            // Handle unique constraint violation specifically
            if (dbError instanceof Error) {
              // Prisma unique constraint error codes
              if (
                dbError.message.includes("Unique constraint") ||
                dbError.message.includes("unique constraint") ||
                dbError.message.includes("duplicate key")
              ) {
                throw new Error(
                  `Invoice "${invoiceRef}" sudah ada untuk user ini`
                );
              }
            }
            // Re-throw other database errors
            throw dbError;
          }
        });

        // Akumulasi hasil
        totalPurchasesCreated += result.purchasesCreated;
        totalSuppliersCreated += result.suppliersCreated;
      } catch (error) {
        console.error(
          `[IMPORT] Error processing row ${processedRow.rowIndex}:`,
          error
        );
        const errorMessage =
          error instanceof Error ? error.message : "Error tidak diketahui";
        processErrors.push(`Baris ${processedRow.rowIndex}: ${errorMessage}`);

        failedTransactions.push({
          rowIndex: processedRow.rowIndex,
          productName: sanitizeString(processedRow.data["Nama Produk"]),
          reason: errorMessage,
          invoiceRef: processedRow.invoiceRef,
        });
      }
    }

    // Kompilasi semua error dan transaksi yang gagal
    const allErrors = [
      ...invalidRows.map((row) => `Baris ${row.rowIndex}: ${row.errorMessage}`),
      ...processErrors, // Error dari proses penyimpanan ke DB
      ...(skipConflicts
        ? skippedConflictRows.map(
            (row) =>
              `Baris ${row.rowIndex}: Invoice "${row.invoiceRef}" sudah ada (diabaikan)`
          )
        : []),
      ...(skipMissingInvoices
        ? skippedMissingInvoiceRows.map(
            (row) => `Baris ${row.rowIndex}: Nomor invoice kosong (diabaikan)`
          )
        : []),
    ];

    // Tambahkan transaksi yang gagal dari validasi dan pemrosesan
    const allFailedTransactions: FailedTransaction[] = [
      ...invalidRows.map((row) => ({
        rowIndex: row.rowIndex,
        productName: sanitizeString(row.data["Nama Produk"]),
        reason: row.errorMessage || "Error validasi",
        invoiceRef: row.invoiceRef,
      })),
      ...failedTransactions, // Transaksi gagal dari proses penyimpanan ke DB
      ...(skipConflicts
        ? skippedConflictRows.map((row) => ({
            rowIndex: row.rowIndex,
            productName: sanitizeString(row.data["Nama Produk"]),
            reason: `Invoice "${row.invoiceRef}" sudah ada dalam sistem (diabaikan)`,
            invoiceRef: row.invoiceRef,
          }))
        : []),
      ...(skipMissingInvoices
        ? skippedMissingInvoiceRows.map((row) => ({
            rowIndex: row.rowIndex,
            productName: sanitizeString(row.data["Nama Produk"]),
            reason: "Nomor invoice tidak ada atau kosong (diabaikan)",
            invoiceRef: row.invoiceRef,
          }))
        : []),
    ];

    // Siapkan hasil akhir
    const finalResult: ImportSummary = {
      purchasesCreated: totalPurchasesCreated,
      suppliersCreated: totalSuppliersCreated,
      warehousesCreated: 0, // Belum diimplementasikan
      errors: allErrors,
      skippedRows:
        invalidRows.length +
        skippedConflictRows.length +
        skippedMissingInvoiceRows.length,
      conflictedInvoices: skipConflicts
        ? conflictDetails.duplicateInvoices
        : [], // Hanya sertakan jika memang dilewati
      missingInvoices: skipMissingInvoices
        ? skippedMissingInvoiceRows.length
        : 0, // Hanya sertakan jika memang dilewati
      failedTransactions: allFailedTransactions,
    };

    // Buat notifikasi yang sesuai berdasarkan hasil
    try {
      if (totalPurchasesCreated === 0) {
        // Kegagalan total
        await createSystemNotification(
          "error",
          "Import Pembelian Gagal",
          `Import pembelian gagal. ${allErrors.length} error ditemukan.`,
          false
        );

        return {
          error: "Import gagal sepenuhnya",
          summary: finalResult,
        };
      } else if (allErrors.length > 0) {
        // Keberhasilan sebagian
        const skippedMessage =
          finalResult.skippedRows > 0
            ? ` ${finalResult.skippedRows} baris diabaikan.`
            : "";
        await createSystemNotification(
          "warning",
          "Import Pembelian Sebagian Berhasil",
          `Import selesai: ${totalPurchasesCreated} pembelian berhasil diimpor, ${totalSuppliersCreated} supplier baru dibuat.${skippedMessage}`,
          false
        );
      } else {
        // Keberhasilan penuh
        await createSystemNotification(
          "success",
          "Import Pembelian Berhasil",
          `Import berhasil! ${totalPurchasesCreated} pembelian diimpor, ${totalSuppliersCreated} supplier baru dibuat.`,
          false
        );
      }
    } catch (notificationError) {
      console.error(
        "[IMPORT] Failed to create notification:",
        notificationError
      );
    }

    let successMessage = `Import berhasil! ${totalPurchasesCreated} pembelian berhasil diimpor.`;

    if (skipConflicts && conflictDetails.duplicateInvoices.length > 0) {
      successMessage += ` ${conflictDetails.duplicateInvoices.length} invoice yang bertentangan diabaikan.`;
    }

    if (
      skipMissingInvoices &&
      missingInvoiceDetails.transactionsWithoutInvoice > 0
    ) {
      successMessage += ` ${missingInvoiceDetails.transactionsWithoutInvoice} transaksi tanpa invoice diabaikan.`;
    }

    return {
      success: successMessage,
      summary: finalResult,
    };
  } catch (error) {
    console.error("[IMPORT] System error during import:", error);

    // Buat notifikasi error
    try {
      await createSystemNotification(
        "error",
        "Import Pembelian Error",
        `Terjadi kesalahan sistem: ${error instanceof Error ? error.message : "Unknown error"}`,
        false
      );
    } catch (notificationError) {
      console.error(
        "[IMPORT] Failed to create error notification:",
        notificationError
      );
    }

    return {
      error: "Gagal memproses file import",
      summary: {
        purchasesCreated: 0,
        suppliersCreated: 0,
        warehousesCreated: 0,
        errors: [error instanceof Error ? error.message : "Unknown error"],
        skippedRows: 0,
        conflictedInvoices: [],
        missingInvoices: 0,
        failedTransactions: [],
      },
    };
  }
};

/**
 * @function importPurchasesWithConflictResolution
 * @description Fungsi pembantu untuk mengimpor pembelian dengan resolusi konflik.
 * Ini memanggil `importPurchases` dengan parameter `skipConflicts` diatur ke `true`.
 * Harus dipanggil ketika pengguna memilih untuk melanjutkan meskipun ada konflik.
 * @param {ArrayBuffer} arrayBuffer - Data file Excel.
 * @returns {Promise<ImportResult>} - Hasil import.
 */
export const importPurchasesWithConflictResolution = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  console.log(
    "[IMPORT] Importing with conflict resolution (skipping conflicts)"
  );
  return importPurchases(arrayBuffer, true, false); // Lewati konflik, jangan lewati missing invoice
};

/**
 * @function importPurchasesWithMissingInvoiceResolution
 * @description BARU: Fungsi pembantu untuk mengimpor pembelian dengan resolusi missing invoice.
 * Ini memanggil `importPurchases` dengan parameter `skipMissingInvoices` diatur ke `true`.
 * Harus dipanggil ketika pengguna memilih untuk melanjutkan meskipun ada transaksi tanpa invoice.
 * @param {ArrayBuffer} arrayBuffer - Data file Excel.
 * @returns {Promise<ImportResult>} - Hasil import.
 */
export const importPurchasesWithMissingInvoiceResolution = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  console.log(
    "[IMPORT] Importing with missing invoice resolution (skipping missing invoices)"
  );
  return importPurchases(arrayBuffer, false, true); // Jangan lewati konflik, lewati missing invoice
};

/**
 * @function importPurchasesWithFullResolution
 * @description BARU: Fungsi pembantu untuk mengimpor pembelian dengan resolusi konflik dan missing invoice.
 * Ini memanggil `importPurchases` dengan kedua parameter `skipConflicts` dan `skipMissingInvoices` diatur ke `true`.
 * Harus dipanggil ketika pengguna memilih untuk melanjutkan meskipun ada kedua masalah.
 * @param {ArrayBuffer} arrayBuffer - Data file Excel.
 * @returns {Promise<ImportResult>} - Hasil import.
 */
export const importPurchasesWithFullResolution = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  console.log(
    "[IMPORT] Importing with full resolution (skipping conflicts and missing invoices)"
  );
  return importPurchases(arrayBuffer, true, true); // Lewati konflik, lewati missing invoice
};
