"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { generateSaleId, generateCustomerId } from "@/lib/generate-id";
import { createSystemNotification } from "@/lib/create-system-notification";
import { getNextTransactionNumber } from "@/actions/entities/sales";
import * as XLSX from "xlsx";

interface ImportSummary {
  salesCreated: number;
  customersCreated: number;
  warehousesCreated: number;
  errors: string[];
  skippedRows: number;
  conflictedInvoices: string[];
  missingInvoices: number;
  failedTransactions: FailedTransaction[];
}

interface ImportResult {
  success?: string;
  error?: string;
  summary: ImportSummary;
  hasConflicts?: boolean;
  conflictDetails?: ConflictDetails;
  hasMissingInvoices?: boolean;
  missingInvoiceDetails?: MissingInvoiceDetails;
}

interface ConflictDetails {
  duplicateInvoices: string[];
  affectedRows: number[];
  validRowsCount: number;
  totalRowsCount: number;
}

interface MissingInvoiceDetails {
  transactionsWithoutInvoice: number;
  affectedRows: number[];
  validRowsCount: number;
  totalRowsCount: number;
}

interface FailedTransaction {
  rowIndex: number;
  productName: string;
  reason: string;
  invoiceRef?: string;
}

interface ProcessedRow {
  data: any;
  rowIndex: number;
  invoiceRef?: string;
  isValid: boolean;
  errorMessage?: string;
}

const sanitizeString = (value: any): string => {
  if (value === null || value === undefined || value === "") return "";
  return String(value).trim().substring(0, 255);
};

const sanitizeNumber = (value: any): number => {
  if (value === null || value === undefined || value === "") return 0;
  const num = Number(value);
  return isNaN(num) ? 0 : Math.max(0, num);
};

const sanitizeDate = (value: any): Date => {
  if (value === null || value === undefined || value === "") {
    return new Date();
  }

  if (typeof value === "number") {
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(
      excelEpoch.getTime() + (value - 1) * 24 * 60 * 60 * 1000
    );
    return isNaN(date.getTime()) ? new Date() : date;
  }

  if (typeof value === "string") {
    const date = new Date(value);
    return isNaN(date.getTime()) ? new Date() : date;
  }

  if (value instanceof Date) {
    return isNaN(value.getTime()) ? new Date() : value;
  }

  return new Date();
};

const handleMergedCells = (data: any[][], headers: string[]): any[][] => {
  console.log("[IMPORT] Processing merged cells and grouping by invoice...");

  const processedData = [...data];

  const mergeableColumns = [
    "No. Transaksi",
    "Tanggal Penjualan",
    "Nama Customer",
    "Nomor Telepon",
    "Email Customer",
    "No. Invoice",
    "Status Pembayaran",
    "Total Penjualan",
  ];
  const lastValues: { [key: string]: any } = {};

  for (let rowIndex = 0; rowIndex < processedData.length; rowIndex++) {
    const row = processedData[rowIndex];

    headers.forEach((header, colIndex) => {
      if (mergeableColumns.includes(header)) {
        const cellValue = row[colIndex];

        if (
          (cellValue === null || cellValue === undefined || cellValue === "") &&
          lastValues[header]
        ) {
          row[colIndex] = lastValues[header];
          console.log(
            `[IMPORT] Filled merged cell at row ${
              rowIndex + 1
            }, column "${header}" with value: ${lastValues[header]}`
          );
        } else if (
          cellValue !== null &&
          cellValue !== undefined &&
          cellValue !== ""
        ) {
          lastValues[header] = cellValue;
        }
      }
    });
  }

  const groupedData: any[][] = [];
  const invoiceGroups: { [key: string]: any[][] } = {};

  for (let rowIndex = 0; rowIndex < processedData.length; rowIndex++) {
    const row = processedData[rowIndex];
    const invoiceColIndex = headers.indexOf("No. Invoice");
    const transactionColIndex = headers.indexOf("No. Transaksi");
    const dateColIndex = headers.indexOf("Tanggal Penjualan");
    const customerColIndex = headers.indexOf("Nama Customer");

    let groupKey = "";

    if (invoiceColIndex >= 0 && row[invoiceColIndex]) {
      groupKey = String(row[invoiceColIndex]).trim();
    } else {
      const transactionNum =
        transactionColIndex >= 0
          ? String(row[transactionColIndex] || "").trim()
          : "";
      const date =
        dateColIndex >= 0 ? String(row[dateColIndex] || "").trim() : "";
      const customer =
        customerColIndex >= 0 ? String(row[customerColIndex] || "").trim() : "";
      groupKey = `${transactionNum}-${date}-${customer}-${rowIndex}`;
    }

    if (!invoiceGroups[groupKey]) {
      invoiceGroups[groupKey] = [];
    }
    invoiceGroups[groupKey].push(row);
  }

  console.log(
    `[IMPORT] Found ${Object.keys(invoiceGroups).length} invoice groups from ${
      processedData.length
    } rows`
  );

  Object.entries(invoiceGroups).forEach(([groupKey, rows]) => {
    if (rows.length === 1) {
      groupedData.push(rows[0]);
    } else {
      console.log(
        `[IMPORT] Invoice group "${groupKey}" has ${rows.length} products:`
      );
      rows.forEach((row, index) => {
        const productNameColIndex = headers.indexOf("Nama Produk");
        const productName =
          productNameColIndex >= 0
            ? row[productNameColIndex]
            : `Product ${index + 1}`;
        console.log(`  - Product ${index + 1}: ${productName}`);
        groupedData.push(row);
      });
    }
  });

  console.log(
    `[IMPORT] After processing merged cells and grouping: ${groupedData.length} data rows`
  );
  return groupedData;
};

const validateTransactionLimit = (
  saleData: any[]
): { isValid: boolean; errorMessage?: string; uniqueTransactions: number } => {
  const uniqueInvoices = new Set<string>();
  const transactionsWithoutInvoice = new Set<string>();

  saleData.forEach((row) => {
    const invoiceRef = sanitizeString(row["No. Invoice"]);
    const saleDate = sanitizeDate(row["Tanggal Penjualan"]);

    if (invoiceRef) {
      uniqueInvoices.add(invoiceRef);
    } else {
      const customerName =
        sanitizeString(row["Nama Customer"]) || "NO_CUSTOMER";
      const transactionNum =
        sanitizeString(row["No. Transaksi"]) || "NO_TRANSACTION";
      const dateKey = saleDate.toISOString().split("T")[0];
      transactionsWithoutInvoice.add(
        `${dateKey}-${customerName}-${transactionNum}`
      );
    }
  });

  const totalUniqueTransactions =
    uniqueInvoices.size + transactionsWithoutInvoice.size;

  console.log(`[IMPORT] Transaction analysis:`);
  console.log(`  - Unique invoices: ${uniqueInvoices.size}`);
  console.log(
    `  - Transactions without invoice: ${transactionsWithoutInvoice.size}`
  );
  console.log(`  - Total unique transactions: ${totalUniqueTransactions}`);
  console.log(`  - Total data rows: ${saleData.length}`);

  if (totalUniqueTransactions > 100) {
    return {
      isValid: false,
      errorMessage: `Terlalu banyak transaksi. Maksimal 100 transaksi per import. Ditemukan ${totalUniqueTransactions} transaksi dari ${saleData.length} baris data.`,
      uniqueTransactions: totalUniqueTransactions,
    };
  }

  return {
    isValid: true,
    uniqueTransactions: totalUniqueTransactions,
  };
};

const checkMissingInvoices = async (
  saleRows: ProcessedRow[]
): Promise<MissingInvoiceDetails> => {
  console.log("[IMPORT] Checking for missing invoice numbers...");

  const transactionsWithoutInvoice = saleRows.filter((row) => {
    const invoiceRef = row.invoiceRef;
    return !invoiceRef || invoiceRef.trim() === "";
  });

  const affectedRows = transactionsWithoutInvoice.map((row, index) =>
    saleRows.indexOf(row)
  );

  const validRowsCount = saleRows.length - transactionsWithoutInvoice.length;

  console.log(
    `[IMPORT] Missing invoice check results: ${transactionsWithoutInvoice.length} transactions without invoice numbers, ${validRowsCount} transactions with valid invoices`
  );

  return {
    transactionsWithoutInvoice: transactionsWithoutInvoice.length,
    affectedRows,
    validRowsCount,
    totalRowsCount: saleRows.length,
  };
};

const checkInvoiceConflicts = async (
  saleRows: ProcessedRow[],
  effectiveUserId: string
): Promise<ConflictDetails> => {
  console.log("[IMPORT] Checking for invoice conflicts...");

  const invoiceRefs = saleRows
    .map((row) => row.invoiceRef)
    .filter(
      (ref): ref is string =>
        ref !== null && ref !== undefined && ref.trim() !== ""
    );

  const uniqueInvoiceRefs = [...new Set(invoiceRefs)];

  if (uniqueInvoiceRefs.length === 0) {
    console.log(
      "[IMPORT] No valid invoice references found, skipping conflict check"
    );
    return {
      duplicateInvoices: [],
      affectedRows: [],
      validRowsCount: saleRows.length,
      totalRowsCount: saleRows.length,
    };
  }

  const existingSales = await db.sale.findMany({
    where: {
      userId: effectiveUserId,
      invoiceRef: {
        in: uniqueInvoiceRefs,
      },
    },
    select: {
      invoiceRef: true,
    },
  });

  const existingInvoiceRefs = new Set(
    existingSales
      .map((s) => s.invoiceRef)
      .filter(
        (ref): ref is string =>
          ref !== null && ref !== undefined && ref.trim() !== ""
      )
  );

  console.log(
    `[IMPORT] Found ${existingInvoiceRefs.size} existing invoices that conflict`
  );

  const duplicateInvoices = Array.from(existingInvoiceRefs);
  const affectedRows = saleRows
    .map((row, index) => ({ ...row, originalIndex: index }))
    .filter((row) => {
      return (
        row.invoiceRef &&
        row.invoiceRef.trim() !== "" &&
        existingInvoiceRefs.has(row.invoiceRef)
      );
    })
    .map((row) => row.originalIndex);

  const validRowsCount = saleRows.length - affectedRows.length;

  console.log(
    `[IMPORT] Conflict check results: ${affectedRows.length} rows have conflicts, ${validRowsCount} rows are valid`
  );

  return {
    duplicateInvoices,
    affectedRows,
    validRowsCount,
    totalRowsCount: saleRows.length,
  };
};

const processAndValidateSaleData = async (
  saleData: any[],
  effectiveUserId: string,
  existingProductMap: Map<string, string>
): Promise<ProcessedRow[]> => {
  console.log("[IMPORT] Processing and validating sale data...");

  const processedRows: ProcessedRow[] = [];

  for (const [index, row] of saleData.entries()) {
    const processedRow: ProcessedRow = {
      data: row,
      rowIndex: row._rowIndex,
      isValid: true,
      errorMessage: undefined,
    };

    try {
      const productName = sanitizeString(row["Nama Produk"]);
      const quantity = sanitizeNumber(row["Quantity"]);
      const salePrice = sanitizeNumber(row["Harga Jual"]);
      const invoiceRef = sanitizeString(row["No. Invoice"]);

      processedRow.invoiceRef = invoiceRef || undefined;

      if (!productName) {
        processedRow.isValid = false;
        processedRow.errorMessage = "Nama Produk tidak boleh kosong";
      } else if (!invoiceRef || invoiceRef.trim() === "") {
        processedRow.isValid = false;
        processedRow.errorMessage =
          "No. Invoice tidak boleh kosong dan harus diisi";
      } else if (quantity <= 0) {
        processedRow.isValid = false;
        processedRow.errorMessage = "Quantity harus lebih dari 0";
      } else if (salePrice <= 0) {
        processedRow.isValid = false;
        processedRow.errorMessage = "Harga Jual harus lebih dari 0";
      } else {
        if (!existingProductMap.has(productName)) {
          processedRow.isValid = false;
          processedRow.errorMessage = `Produk "${productName}" tidak ditemukan (case-sensitive)`;

          const similarProducts = Array.from(existingProductMap.keys()).filter(
            (name) => name.toLowerCase() === productName.toLowerCase()
          );

          if (similarProducts.length > 0) {
            processedRow.errorMessage += `. Mungkin maksud Anda: ${similarProducts.join(
              ", "
            )}`;
          }
        }
      }
    } catch (error) {
      console.error(
        `[IMPORT] Error validating row ${processedRow.rowIndex}:`,
        error
      );
      processedRow.isValid = false;
      processedRow.errorMessage = `Error validasi: ${
        error instanceof Error ? error.message : "Unknown error"
      }`;
    }

    processedRows.push(processedRow);
  }

  const validCount = processedRows.filter((row) => row.isValid).length;
  console.log(
    `[IMPORT] Validation complete: ${validCount}/${processedRows.length} rows valid`
  );

  return processedRows;
};

export const importSales = async (
  arrayBuffer: ArrayBuffer,
  skipConflicts: boolean = false,
  skipMissingInvoices: boolean = false
): Promise<ImportResult> => {
  try {
    console.log(
      "[IMPORT] Starting enhanced sale import process with invoice validation"
    );

    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return {
        error: "Tidak terautentikasi",
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          warehousesCreated: 0,
          errors: ["User tidak terautentikasi"],
          skippedRows: 0,
          conflictedInvoices: [],
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    const workbook = XLSX.read(arrayBuffer, { type: "array" });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    console.log(`[IMPORT] Parsed ${data.length} rows from Excel`);

    if (data.length < 2) {
      return {
        error: "File Excel kosong atau tidak memiliki data",
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          warehousesCreated: 0,
          errors: ["File tidak memiliki data yang valid"],
          skippedRows: 0,
          conflictedInvoices: [],
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    let headerRowIndex = -1;
    let headers: string[] = [];

    const expectedHeaders = [
      "No. Transaksi",
      "Tanggal Penjualan",
      "No. Invoice",
      "Nama Customer",
    ];

    for (let i = 0; i < Math.min(10, data.length); i++) {
      const row = data[i] as string[];
      if (row && Array.isArray(row)) {
        const rowStrings = row.map((cell) =>
          cell ? cell.toString().trim() : ""
        );

        const foundHeaders = expectedHeaders.filter((expectedHeader) =>
          rowStrings.some(
            (cell) =>
              cell.toLowerCase().includes(expectedHeader.toLowerCase()) ||
              expectedHeader.toLowerCase().includes(cell.toLowerCase())
          )
        );

        if (foundHeaders.length >= 2) {
          headerRowIndex = i;
          headers = rowStrings.filter((cell) => cell !== "");
          console.log(`[IMPORT] Found headers at row ${i + 1}:`, headers);
          break;
        }
      }
    }

    if (headerRowIndex === -1 || headers.length === 0) {
      return {
        error:
          "Header tidak ditemukan. Pastikan file menggunakan template yang benar dengan header: No. Transaksi, Tanggal Penjualan, No. Invoice, Nama Customer, dll.",
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          warehousesCreated: 0,
          errors: ["Header tidak valid atau tidak ditemukan"],
          skippedRows: 0,
          conflictedInvoices: [],
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    console.log(
      `[IMPORT] Headers found at row ${headerRowIndex + 1}:`,
      headers
    );

    let dataRows = data.slice(headerRowIndex + 1) as any[][];

    dataRows = dataRows.filter((row: any[]) => {
      if (!Array.isArray(row)) return false;

      return row.some(
        (cell) =>
          cell !== null && cell !== undefined && cell.toString().trim() !== ""
      );
    });

    console.log(
      `[IMPORT] Found ${dataRows.length} non-empty data rows after header row ${
        headerRowIndex + 1
      }`
    );

    dataRows = handleMergedCells(dataRows, headers);

    const saleData = dataRows
      .map((row: any[], index: number) => {
        const obj: any = {};
        headers.forEach((header, i) => {
          obj[header] = row[i];
        });
        obj._rowIndex = headerRowIndex + index + 2;
        return obj;
      })
      .filter((row) => {
        const hasProductName =
          row["Nama Produk"] && String(row["Nama Produk"]).trim() !== "";
        const hasQuantity =
          row["Quantity"] &&
          !isNaN(Number(row["Quantity"])) &&
          Number(row["Quantity"]) > 0;
        const hasPrice =
          row["Harga Jual"] &&
          !isNaN(Number(row["Harga Jual"])) &&
          Number(row["Harga Jual"]) > 0;

        return hasProductName && (hasQuantity || hasPrice);
      });

    console.log(
      `[IMPORT] Extracted ${saleData.length} valid data rows after processing`
    );

    if (saleData.length === 0) {
      return {
        error: "Tidak ada data penjualan yang valid ditemukan",
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          warehousesCreated: 0,
          errors: ["Tidak ada baris data yang ditemukan"],
          skippedRows: 0,
          conflictedInvoices: [],
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    const validationResult = validateTransactionLimit(saleData);

    if (!validationResult.isValid) {
      return {
        error: validationResult.errorMessage,
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          warehousesCreated: 0,
          errors: [validationResult.errorMessage || "Validation failed"],
          skippedRows: 0,
          conflictedInvoices: [],
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    console.log(
      `[IMPORT] Validated ${validationResult.uniqueTransactions} unique transactions from ${saleData.length} data rows`
    );

    console.log(
      "[IMPORT] Fetching existing products for case-sensitive matching..."
    );
    const existingProducts = await db.product.findMany({
      where: {
        userId: effectiveUserId,
      },
      select: {
        id: true,
        name: true,
      },
    });

    const existingProductMap = new Map(
      existingProducts.map((p) => [p.name, p.id])
    );

    console.log(
      `[IMPORT] Loaded ${existingProductMap.size} existing products for matching`
    );

    const processedRows = await processAndValidateSaleData(
      saleData,
      effectiveUserId,
      existingProductMap
    );

    const missingInvoiceDetails = await checkMissingInvoices(processedRows);

    if (
      missingInvoiceDetails.transactionsWithoutInvoice > 0 &&
      !skipMissingInvoices
    ) {
      console.log(
        `[IMPORT] Found missing invoices, returning missing invoice details for user decision`
      );
      return {
        hasMissingInvoices: true,
        missingInvoiceDetails,
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          warehousesCreated: 0,
          errors: [],
          skippedRows: missingInvoiceDetails.transactionsWithoutInvoice,
          conflictedInvoices: [],
          missingInvoices: missingInvoiceDetails.transactionsWithoutInvoice,
          failedTransactions: [],
        },
      };
    }

    const conflictDetails = await checkInvoiceConflicts(
      processedRows,
      effectiveUserId
    );

    if (conflictDetails.duplicateInvoices.length > 0 && !skipConflicts) {
      console.log(
        `[IMPORT] Found conflicts, returning conflict details for user decision`
      );
      return {
        hasConflicts: true,
        conflictDetails,
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          warehousesCreated: 0,
          errors: [],
          skippedRows: conflictDetails.affectedRows.length,
          conflictedInvoices: conflictDetails.duplicateInvoices,
          missingInvoices: 0,
          failedTransactions: [],
        },
      };
    }

    let validRows = processedRows.filter((row) => row.isValid);

    if (skipConflicts && conflictDetails.duplicateInvoices.length > 0) {
      console.log("[IMPORT] Skipping conflicted invoice rows as requested");
      validRows = validRows.filter(
        (row) =>
          !row.invoiceRef ||
          !conflictDetails.duplicateInvoices.includes(row.invoiceRef)
      );
    }

    if (
      skipMissingInvoices &&
      missingInvoiceDetails.transactionsWithoutInvoice > 0
    ) {
      console.log("[IMPORT] Skipping rows with missing invoices as requested");
      validRows = validRows.filter(
        (row) => row.invoiceRef && row.invoiceRef.trim() !== ""
      );
    }

    const invalidRows = processedRows.filter((row) => !row.isValid);
    const skippedConflictRows = skipConflicts
      ? processedRows.filter(
          (row) =>
            row.invoiceRef &&
            conflictDetails.duplicateInvoices.includes(row.invoiceRef)
        )
      : [];
    const skippedMissingInvoiceRows = skipMissingInvoices
      ? processedRows.filter(
          (row) => !row.invoiceRef || row.invoiceRef.trim() === ""
        )
      : [];

    console.log(
      `[IMPORT] Processing: ${validRows.length} valid rows, ${invalidRows.length} invalid rows, ${skippedConflictRows.length} skipped due to conflicts, ${skippedMissingInvoiceRows.length} skipped due to missing invoices`
    );

    if (validRows.length === 0) {
      const allErrors = [
        ...invalidRows.map(
          (row) => `Baris ${row.rowIndex}: ${row.errorMessage}`
        ),
        ...skippedConflictRows.map(
          (row) =>
            `Baris ${row.rowIndex}: Invoice "${row.invoiceRef}" sudah ada`
        ),
        ...skippedMissingInvoiceRows.map(
          (row) => `Baris ${row.rowIndex}: Tidak ada nomor invoice`
        ),
      ];

      const failedTransactions: FailedTransaction[] = [
        ...invalidRows.map((row) => ({
          rowIndex: row.rowIndex,
          productName: sanitizeString(row.data["Nama Produk"]),
          reason: row.errorMessage || "Error tidak diketahui",
          invoiceRef: row.invoiceRef,
        })),
        ...skippedConflictRows.map((row) => ({
          rowIndex: row.rowIndex,
          productName: sanitizeString(row.data["Nama Produk"]),
          reason: `Invoice "${row.invoiceRef}" sudah ada dalam sistem`,
          invoiceRef: row.invoiceRef,
        })),
        ...skippedMissingInvoiceRows.map((row) => ({
          rowIndex: row.rowIndex,
          productName: sanitizeString(row.data["Nama Produk"]),
          reason: "Nomor invoice tidak ada atau kosong",
          invoiceRef: row.invoiceRef,
        })),
      ];

      return {
        error: "Tidak ada data valid untuk diimpor",
        summary: {
          salesCreated: 0,
          customersCreated: 0,
          warehousesCreated: 0,
          errors: allErrors,
          skippedRows:
            invalidRows.length +
            skippedConflictRows.length +
            skippedMissingInvoiceRows.length,
          conflictedInvoices: conflictDetails.duplicateInvoices,
          missingInvoices: skippedMissingInvoiceRows.length,
          failedTransactions,
        },
      };
    }

    let totalSalesCreated = 0;
    let totalCustomersCreated = 0;
    const processErrors: string[] = [];
    const failedTransactions: FailedTransaction[] = [];

    console.log(`[IMPORT] Starting to process ${validRows.length} valid rows`);

    for (const processedRow of validRows) {
      try {
        const result = await db.$transaction(async (tx) => {
          const row = processedRow.data;

          const saleDate = sanitizeDate(row["Tanggal Penjualan"]);
          const productName = sanitizeString(row["Nama Produk"]);
          const quantity = sanitizeNumber(row["Quantity"]);
          const salePrice = sanitizeNumber(row["Harga Jual"]);
          const customerName = sanitizeString(row["Nama Customer"]);
          const customerPhone = sanitizeString(row["Nomor Telepon"]);
          const customerEmail = sanitizeString(row["Email Customer"]);
          const discountPercentage = sanitizeNumber(row["Diskon (%)"]);
          const discountAmount = sanitizeNumber(row["Diskon (Rp)"]);
          const taxPercentage = sanitizeNumber(row["PPN (%)"]);
          const statusValue = sanitizeString(row["Status Pembayaran"]);
          const memo = sanitizeString(row["Memo"]);
          const invoiceRef = sanitizeString(row["No. Invoice"]);
          const unit = sanitizeString(row["Satuan"]) || "Pcs";

          const productId = existingProductMap.get(productName);
          if (!productId) {
            throw new Error(
              `Produk "${productName}" tidak ditemukan (case-sensitive)`
            );
          }

          let customerId: string | null = null;
          let customersCreated = 0;

          if (customerName) {
            let customer = await tx.customer.findFirst({
              where: {
                name: customerName,
                userId: effectiveUserId,
              },
            });

            if (!customer) {
              const newCustomerId = await generateCustomerId();
              customer = await tx.customer.create({
                data: {
                  id: newCustomerId,
                  name: customerName,
                  phone: customerPhone || null,
                  email: customerEmail || null,
                  userId: effectiveUserId,
                },
              });
              customersCreated = 1;
              console.log(`[IMPORT] Created new customer: ${customerName}`);
            }
            customerId = customer.id;
          }

          const subtotal = quantity * salePrice;
          const finalDiscountAmount =
            discountAmount > 0
              ? discountAmount
              : (subtotal * discountPercentage) / 100;
          const afterDiscount = subtotal - finalDiscountAmount;
          const taxAmount = (afterDiscount * taxPercentage) / 100;
          const totalAmount = afterDiscount + taxAmount;

          const status = statusValue === "Lunas" ? "LUNAS" : "BELUM_LUNAS";

          const saleId = await generateSaleId(effectiveUserId);
          const transactionNumberResult = await getNextTransactionNumber(
            "TRX",
            saleDate
          );
          const transactionNumber = transactionNumberResult.success
            ? transactionNumberResult.nextNumber
            : `JUAL-${Date.now()}`;

          try {
            const sale = await tx.sale.create({
              data: {
                id: saleId,
                saleDate,
                totalAmount,
                transactionNumber,
                invoiceRef: invoiceRef || null,
                memo: memo || null,
                status: status as any,
                userId: effectiveUserId,
                customerId,
                isDraft: false,
              },
            });

            console.log(
              `[IMPORT] Created sale: ${sale.transactionNumber} for product: ${productName}`
            );

            await tx.saleItem.create({
              data: {
                quantity,
                priceAtSale: salePrice,
                unit,
                discountPercentage:
                  discountPercentage > 0 ? discountPercentage : null,
                discountAmount:
                  finalDiscountAmount > 0 ? finalDiscountAmount : null,
                tax: taxPercentage > 0 ? `${taxPercentage}%` : null,
                saleId: sale.id,
                productId,
              },
            });

            // Check if product has enough stock before decrementing
            const product = await tx.product.findUnique({
              where: { id: productId },
              select: { stock: true, name: true },
            });

            if (!product) {
              throw new Error(`Product ${productName} not found`);
            }

            if (product.stock < quantity) {
              throw new Error(
                `Stock tidak mencukupi untuk produk "${productName}". Stock tersedia: ${product.stock}, diminta: ${quantity}`
              );
            }

            await tx.product.update({
              where: { id: productId },
              data: {
                stock: {
                  decrement: quantity,
                },
              },
            });

            return {
              salesCreated: 1,
              customersCreated,
            };
          } catch (dbError) {
            if (dbError instanceof Error) {
              if (
                dbError.message.includes("Unique constraint") ||
                dbError.message.includes("unique constraint") ||
                dbError.message.includes("duplicate key")
              ) {
                throw new Error(
                  `Invoice "${invoiceRef}" sudah ada untuk user ini`
                );
              }
            }
            throw dbError;
          }
        });

        totalSalesCreated += result.salesCreated;
        totalCustomersCreated += result.customersCreated;
      } catch (error) {
        console.error(
          `[IMPORT] Error processing row ${processedRow.rowIndex}:`,
          error
        );
        const errorMessage =
          error instanceof Error ? error.message : "Error tidak diketahui";
        processErrors.push(`Baris ${processedRow.rowIndex}: ${errorMessage}`);

        failedTransactions.push({
          rowIndex: processedRow.rowIndex,
          productName: sanitizeString(processedRow.data["Nama Produk"]),
          reason: errorMessage,
          invoiceRef: processedRow.invoiceRef,
        });
      }
    }

    const allErrors = [
      ...invalidRows.map((row) => `Baris ${row.rowIndex}: ${row.errorMessage}`),
      ...processErrors,
      ...(skipConflicts
        ? skippedConflictRows.map(
            (row) =>
              `Baris ${row.rowIndex}: Invoice "${row.invoiceRef}" sudah ada (diabaikan)`
          )
        : []),
      ...(skipMissingInvoices
        ? skippedMissingInvoiceRows.map(
            (row) => `Baris ${row.rowIndex}: Nomor invoice kosong (diabaikan)`
          )
        : []),
    ];

    const allFailedTransactions: FailedTransaction[] = [
      ...invalidRows.map((row) => ({
        rowIndex: row.rowIndex,
        productName: sanitizeString(row.data["Nama Produk"]),
        reason: row.errorMessage || "Error validasi",
        invoiceRef: row.invoiceRef,
      })),
      ...failedTransactions,
      ...(skipConflicts
        ? skippedConflictRows.map((row) => ({
            rowIndex: row.rowIndex,
            productName: sanitizeString(row.data["Nama Produk"]),
            reason: `Invoice "${row.invoiceRef}" sudah ada dalam sistem (diabaikan)`,
            invoiceRef: row.invoiceRef,
          }))
        : []),
      ...(skipMissingInvoices
        ? skippedMissingInvoiceRows.map((row) => ({
            rowIndex: row.rowIndex,
            productName: sanitizeString(row.data["Nama Produk"]),
            reason: "Nomor invoice tidak ada atau kosong (diabaikan)",
            invoiceRef: row.invoiceRef,
          }))
        : []),
    ];

    const finalResult: ImportSummary = {
      salesCreated: totalSalesCreated,
      customersCreated: totalCustomersCreated,
      warehousesCreated: 0,
      errors: allErrors,
      skippedRows:
        invalidRows.length +
        skippedConflictRows.length +
        skippedMissingInvoiceRows.length,
      conflictedInvoices: skipConflicts
        ? conflictDetails.duplicateInvoices
        : [],
      missingInvoices: skipMissingInvoices
        ? skippedMissingInvoiceRows.length
        : 0,
      failedTransactions: allFailedTransactions,
    };

    try {
      if (totalSalesCreated === 0) {
        await createSystemNotification(
          "error",
          "Import Penjualan Gagal",
          `Import penjualan gagal. ${allErrors.length} error ditemukan.`,
          false
        );

        return {
          error: "Import gagal sepenuhnya",
          summary: finalResult,
        };
      } else if (allErrors.length > 0) {
        const skippedMessage =
          finalResult.skippedRows > 0
            ? ` ${finalResult.skippedRows} baris diabaikan.`
            : "";
        await createSystemNotification(
          "warning",
          "Import Penjualan Sebagian Berhasil",
          `Import selesai: ${totalSalesCreated} penjualan berhasil diimpor, ${totalCustomersCreated} customer baru dibuat.${skippedMessage}`,
          false
        );
      } else {
        await createSystemNotification(
          "success",
          "Import Penjualan Berhasil",
          `Import berhasil! ${totalSalesCreated} penjualan diimpor, ${totalCustomersCreated} customer baru dibuat.`,
          false
        );
      }
    } catch (notificationError) {
      console.error(
        "[IMPORT] Failed to create notification:",
        notificationError
      );
    }

    let successMessage = `Import berhasil! ${totalSalesCreated} penjualan berhasil diimpor.`;

    if (skipConflicts && conflictDetails.duplicateInvoices.length > 0) {
      successMessage += ` ${conflictDetails.duplicateInvoices.length} invoice yang bertentangan diabaikan.`;
    }

    if (
      skipMissingInvoices &&
      missingInvoiceDetails.transactionsWithoutInvoice > 0
    ) {
      successMessage += ` ${missingInvoiceDetails.transactionsWithoutInvoice} transaksi tanpa invoice diabaikan.`;
    }

    return {
      success: successMessage,
      summary: finalResult,
    };
  } catch (error) {
    console.error("[IMPORT] System error during import:", error);

    try {
      await createSystemNotification(
        "error",
        "Import Penjualan Error",
        `Terjadi kesalahan sistem: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        false
      );
    } catch (notificationError) {
      console.error(
        "[IMPORT] Failed to create error notification:",
        notificationError
      );
    }

    return {
      error: "Gagal memproses file import",
      summary: {
        salesCreated: 0,
        customersCreated: 0,
        warehousesCreated: 0,
        errors: [error instanceof Error ? error.message : "Unknown error"],
        skippedRows: 0,
        conflictedInvoices: [],
        missingInvoices: 0,
        failedTransactions: [],
      },
    };
  }
};

export const importSalesWithConflictResolution = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  console.log(
    "[IMPORT] Importing with conflict resolution (skipping conflicts)"
  );
  return importSales(arrayBuffer, true, false);
};

export const importSalesWithMissingInvoiceResolution = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  console.log(
    "[IMPORT] Importing with missing invoice resolution (skipping missing invoices)"
  );
  return importSales(arrayBuffer, false, true);
};

export const importSalesWithFullResolution = async (
  arrayBuffer: ArrayBuffer
): Promise<ImportResult> => {
  console.log(
    "[IMPORT] Importing with full resolution (skipping conflicts and missing invoices)"
  );
  return importSales(arrayBuffer, true, true);
};
