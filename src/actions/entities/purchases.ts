"use server";

import { z } from "zod";
import { PurchaseSchema } from "@/schemas/zod";
import { db } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { createPurchaseSuccessNotification } from "@/lib/create-system-notification";
import { generatePurchaseId } from "@/lib/generate-id";
import { canCreateTransaction } from "@/lib/subscription-limits";
import { getUserSubscription } from "@/lib/subscription";
import { StockMovementType } from "@/types/warehouse";

/**
 * Fungsi untuk mendapatkan nomor transaksi berikutnya yang unik secara global.
 * Fungsi ini membuat format nomor yang berbeda berdasarkan prefix yang diberikan ('TRX' untuk pembelian, 'INV' untuk faktur).
 * @param {string} prefix - Prefix untuk nomor transaksi (misalnya, "TRX", "INV").
 * @param {Date | string} [date] - Tanggal yang digunakan sebagai basis untuk tahun transaksi. Jika tidak ada, tanggal saat ini akan digunakan.
 * @returns {Promise<{success: boolean, nextNumber: string}>} - Objek yang berisi status keberhasilan dan nomor transaksi berikutnya.
 */
export const getNextTransactionNumber = async (
  prefix: string,
  date?: Date | string
) => {
  try {
    // Gunakan tanggal yang diberikan atau tanggal hari ini.
    const baseDate = date ? new Date(date) : new Date();
    const year = baseDate.getFullYear();
    const yearSuffix = String(year).slice(-2); // Ambil 2 digit terakhir dari tahun.

    let newPrefix: string;
    let regexPattern: RegExp;

    // Tentukan prefix baru dan pola regex berdasarkan input prefix.
    if (prefix.toUpperCase() === "TRX") {
      // Untuk transaksi pembelian: formatnya BELI-{YY}B{NNNNNN}
      newPrefix = "BELI";
      regexPattern = new RegExp(`^BELI-${yearSuffix}B\\d{6}$`);
    } else if (prefix.toUpperCase() === "INV") {
      // Untuk referensi faktur: formatnya INV-{YY}B{NNNNNN}
      newPrefix = "INV";
      regexPattern = new RegExp(`^INV-${yearSuffix}B\\d{6}$`);
    } else {
      // Untuk prefix lain, pertahankan perilaku lama.
      newPrefix = prefix.toUpperCase();
      regexPattern = new RegExp(
        `^${prefix.toUpperCase()}-${yearSuffix}B\\d{6}$`
      );
    }

    // Hitung secara GLOBAL di semua pengguna untuk nomor transaksi yang dibuat otomatis.
    // Ini memastikan keunikan global dan mengecualikan entri manual.
    if (prefix.toUpperCase() === "TRX") {
      // Ambil semua nomor transaksi pembelian yang cocok dengan pola.
      const purchasesWithAutoTrx = await db.purchase.findMany({
        where: {
          transactionNumber: {
            startsWith: `BELI-${yearSuffix}B`,
          },
        },
        select: {
          transactionNumber: true,
        },
      });

      // Filter untuk menghitung hanya nomor yang dibuat secara berurutan.
      const autoGeneratedCount = purchasesWithAutoTrx.filter(
        (purchase) =>
          purchase.transactionNumber &&
          regexPattern.test(purchase.transactionNumber)
      ).length;

      const nextNumber = autoGeneratedCount + 1;
      const formattedNumber = String(nextNumber).padStart(6, "0");
      const result = `${newPrefix}-${yearSuffix}B${formattedNumber}`;

      return {
        success: true,
        nextNumber: result,
      };
    } else if (prefix.toUpperCase() === "INV") {
      // Untuk referensi faktur, pastikan keunikan global di penjualan dan pembelian.
      const purchasesWithAutoInv = await db.purchase.findMany({
        where: {
          invoiceRef: {
            startsWith: `INV-${yearSuffix}B`,
          },
        },
        select: {
          invoiceRef: true,
        },
      });

      const salesWithAutoInv = await db.sale.findMany({
        where: {
          invoiceRef: {
            startsWith: `INV-${yearSuffix}`,
          },
        },
        select: {
          invoiceRef: true,
        },
      });

      // Gabungkan dan filter untuk menghitung hanya nomor yang dibuat otomatis.
      const globalInvoiceRegex = new RegExp(`^INV-${yearSuffix}[JB]\\d{6}$`);
      const allAutoInvoices = [
        ...purchasesWithAutoInv.map((p) => p.invoiceRef),
        ...salesWithAutoInv.map((s) => s.invoiceRef),
      ].filter(
        (invoiceRef) => invoiceRef && globalInvoiceRegex.test(invoiceRef)
      );

      const nextNumber = allAutoInvoices.length + 1;
      const formattedNumber = String(nextNumber).padStart(6, "0");
      const result = `${newPrefix}-${yearSuffix}B${formattedNumber}`;

      return {
        success: true,
        nextNumber: result,
      };
    } else {
      // Pertahankan format lama untuk prefix lain.
      const purchasesWithOldTrx = await db.purchase.findMany({
        where: {
          transactionNumber: {
            startsWith: `${prefix.toUpperCase()}-${yearSuffix}`,
          },
        },
        select: {
          transactionNumber: true,
        },
      });

      const autoGeneratedCount = purchasesWithOldTrx.filter(
        (purchase) =>
          purchase.transactionNumber &&
          regexPattern.test(purchase.transactionNumber)
      ).length;

      const nextNumber = autoGeneratedCount + 1;
      const formattedNumber = `B${String(nextNumber).padStart(6, "0")}`;
      const result = `${prefix.toUpperCase()}-${yearSuffix}${formattedNumber}`;

      return {
        success: true,
        nextNumber: result,
      };
    }
  } catch (error) {
    console.error("Error generating next transaction number:", error);
    // Fallback jika terjadi error.
    const fallbackDate = date ? new Date(date) : new Date();
    const year = fallbackDate.getFullYear();
    const yearSuffix = String(year).slice(-2);
    let fallbackNumber: string;
    if (prefix.toUpperCase() === "TRX") {
      fallbackNumber = `BELI-${yearSuffix}B000001`;
    } else if (prefix.toUpperCase() === "INV") {
      fallbackNumber = `INV-${yearSuffix}B000001`;
    } else {
      fallbackNumber = `${prefix.toUpperCase()}-${yearSuffix}B000001`;
    }
    return {
      success: true,
      nextNumber: fallbackNumber,
    };
  }
};

/**
 * Fungsi untuk menambahkan data pembelian baru ke dalam database.
 * @param {z.infer<typeof PurchaseSchema>} values - Data pembelian yang divalidasi oleh Zod schema.
 * @returns {Promise<{success?: string, error?: string, data?: object}>} - Objek yang berisi pesan sukses atau error, dan data pembelian yang baru dibuat.
 */
export const addPurchase = async (values: z.infer<typeof PurchaseSchema>) => {
  // Dapatkan ID pengguna yang efektif (pemilik atau karyawan).
  const effectiveUserId = await getEffectiveUserId();
  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Periksa batas langganan sebelum membuat pembelian.
  try {
    const userSubscription = await getUserSubscription(userId);
    const limitCheck = await canCreateTransaction(
      userId,
      userSubscription.plan
    );
    if (!limitCheck.allowed) {
      return {
        error:
          limitCheck.message ||
          "Batas transaksi bulanan tercapai untuk paket Anda.",
      };
    }
  } catch (error) {
    console.error("Error checking subscription limits:", error);
    return { error: "Gagal memeriksa batas langganan." };
  }

  // 2. Validasi input di sisi server.
  const validatedFields = PurchaseSchema.safeParse(values);
  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    items,
    totalAmount,
    invoiceRef,
    supplierId: rawSupplierId,
    isDraft,
    supplierEmail,
    transactionDate,
    paymentDueDate,
    transactionNumber,
    tags,
    billingAddress,
    warehouseId,
    memo,
    lampiran,
  } = validatedFields.data;

  const supplierId = rawSupplierId ? rawSupplierId : null;

  try {
    // 3. Buat pembelian dalam database menggunakan transaksi untuk memastikan integritas data.
    const result = await db.$transaction(async (tx) => {
      // Buat ID pembelian kustom.
      const customPurchaseId = await generatePurchaseId(userId);

      // Buat record pembelian.
      const purchase = await tx.purchase.create({
        data: {
          id: customPurchaseId,
          totalAmount,
          invoiceRef,
          userId,
          supplierId,
          isDraft: isDraft || false,
          supplierEmail,
          transactionDate,
          paymentDueDate,
          transactionNumber: transactionNumber || customPurchaseId,
          tags: tags || [],
          billingAddress,
          warehouseId,
          memo: memo || "",
          lampiran: lampiran || [],
          items: {
            create: items.map((item) => ({
              quantity: item.quantity,
              costAtPurchase: item.costAtPurchase,
              productId: item.productId,
              unit: item.unit || "Buah",
              tax: item.tax || null,
              discountPercentage: item.discountPercentage || null,
              discountAmount: item.discountAmount || null,
            })),
          },
        },
        include: {
          items: true,
        },
      });

      // Perbarui stok produk dan stok gudang jika bukan draft.
      if (!isDraft && warehouseId) {
        for (const item of items) {
          // Perbarui stok produk umum.
          await tx.product.update({
            where: { id: item.productId },
            data: { stock: { increment: item.quantity } },
          });

          // Perbarui atau buat stok di gudang.
          const existingWarehouseStock = await tx.warehouseStock.findUnique({
            where: {
              productId_warehouseId: {
                productId: item.productId,
                warehouseId: warehouseId,
              },
            },
          });

          if (existingWarehouseStock) {
            await tx.warehouseStock.update({
              where: {
                productId_warehouseId: {
                  productId: item.productId,
                  warehouseId: warehouseId,
                },
              },
              data: { quantity: { increment: item.quantity } },
            });
          } else {
            await tx.warehouseStock.create({
              data: {
                productId: item.productId,
                warehouseId: warehouseId,
                quantity: item.quantity,
                minLevel: 0,
              },
            });
          }

          // Buat catatan pergerakan stok.
          await tx.stockMovement.create({
            data: {
              type: StockMovementType.PURCHASE,
              quantity: item.quantity,
              previousStock: existingWarehouseStock?.quantity || 0,
              newStock: (existingWarehouseStock?.quantity || 0) + item.quantity,
              reference: purchase.id,
              notes: `Purchase: ${purchase.transactionNumber || purchase.id}`,
              productId: item.productId,
              warehouseId: warehouseId,
              userId,
            },
          });
        }
      } else if (!isDraft) {
        // Jika tidak ada gudang, hanya perbarui stok produk umum.
        for (const item of items) {
          await tx.product.update({
            where: { id: item.productId },
            data: { stock: { increment: item.quantity } },
          });
        }
      }

      return purchase;
    });

    // 4. Buat notifikasi sukses.
    await createPurchaseSuccessNotification(
      result.id,
      result.totalAmount.toNumber()
    );

    // 5. Revalidasi cache halaman pembelian.
    revalidatePath("/dashboard/purchases");

    // Ambil data pembelian yang sudah diperbarui.
    const updatedPurchase = await db.purchase.findUnique({
      where: { id: result.id },
      include: { items: true },
    });

    if (!updatedPurchase) {
      return { error: "Gagal mengambil data pembelian yang dibuat." };
    }

    // Serialisasi hasil untuk dikirim ke client (konversi Decimal ke number, Date ke string).
    const serializedResult = {
      id: updatedPurchase.id,
      purchaseDate: updatedPurchase.purchaseDate.toISOString(),
      totalAmount: updatedPurchase.totalAmount.toNumber(),
      invoiceRef: updatedPurchase.invoiceRef,
      isDraft: updatedPurchase.isDraft,
      createdAt: updatedPurchase.createdAt.toISOString(),
      updatedAt: updatedPurchase.updatedAt.toISOString(),
      userId: updatedPurchase.userId,
      supplierId: updatedPurchase.supplierId,
      supplierEmail: updatedPurchase.supplierEmail,
      transactionDate: updatedPurchase.transactionDate?.toISOString(),
      paymentDueDate: updatedPurchase.paymentDueDate?.toISOString(),
      transactionNumber: updatedPurchase.transactionNumber,
      tags: updatedPurchase.tags,
      billingAddress: updatedPurchase.billingAddress,
      memo: updatedPurchase.memo,
      lampiran: updatedPurchase.lampiran || [],
      warehouseId: warehouseId,
      items: updatedPurchase.items.map((item) => ({
        id: item.id,
        quantity: item.quantity,
        costAtPurchase: item.costAtPurchase.toNumber(),
        purchaseId: item.purchaseId,
        productId: item.productId,
        unit: item.unit,
        tax: item.tax,
        discountPercentage: item.discountPercentage
          ? item.discountPercentage.toNumber()
          : null,
        discountAmount: item.discountAmount
          ? item.discountAmount.toNumber()
          : null,
        createdAt: item.createdAt.toISOString(),
        updatedAt: item.updatedAt.toISOString(),
      })),
    };

    return {
      success: "Pembelian berhasil dicatat!",
      data: serializedResult,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mencatat pembelian. Silakan coba lagi." };
  }
};

/**
 * Fungsi untuk mengambil semua data pembelian milik pengguna.
 * @returns {Promise<{purchases?: object[], error?: string}>} - Objek yang berisi daftar pembelian atau pesan error.
 */
export const getPurchases = async () => {
  // Dapatkan ID pengguna yang efektif.
  const effectiveUserId = await getEffectiveUserId();
  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Ambil semua pembelian milik pengguna dari database.
    const purchases = await db.purchase.findMany({
      where: {
        userId,
      },
      orderBy: {
        purchaseDate: "desc",
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                name: true,
              },
            },
          },
        },
        supplier: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            address: true,
          },
        },
        warehouse: true,
        user: {
          select: {
            id: true,
            name: true,
            businessInfo: {
              select: {
                companyName: true,
                companyAddress: true,
                companyPhone: true,
                companyEmail: true,
              },
            },
          },
        },
      },
    });

    // Serialisasi data untuk client.
    const serializedPurchases = purchases.map((purchase) => {
      const plainPurchase: any = {
        id: purchase.id,
        purchaseDate: purchase.purchaseDate.toISOString(),
        totalAmount: purchase.totalAmount.toNumber(),
        invoiceRef: purchase.invoiceRef,
        isDraft: purchase.isDraft,
        isPublic: purchase.isPublic,
        status: purchase.status,
        createdAt: purchase.createdAt.toISOString(),
        updatedAt: purchase.updatedAt.toISOString(),
        userId: purchase.userId,
        supplierId: purchase.supplierId,
        supplierEmail: purchase.supplierEmail,
        transactionDate: purchase.transactionDate?.toISOString(),
        paymentDueDate: purchase.paymentDueDate?.toISOString(),
        transactionNumber: purchase.transactionNumber,
        tags: purchase.tags,
        billingAddress: purchase.billingAddress,
        memo: purchase.memo,
        lampiran: purchase.lampiran || [],
        warehouseId: purchase.warehouseId,
        user: purchase.user
          ? {
              id: purchase.user.id,
              name: purchase.user.name,
            }
          : null,
        companyInfo: purchase.user?.businessInfo
          ? {
              companyName: purchase.user.businessInfo.companyName,
              companyAddress: purchase.user.businessInfo.companyAddress,
              companyPhone: purchase.user.businessInfo.companyPhone,
              companyEmail: purchase.user.businessInfo.companyEmail,
            }
          : null,
        supplier: purchase.supplier
          ? {
              id: purchase.supplier.id,
              name: purchase.supplier.name,
              phone: purchase.supplier.phone,
              email: purchase.supplier.email,
              address: purchase.supplier.address,
            }
          : null,
        items: purchase.items.map((item) => ({
          id: item.id,
          quantity: item.quantity,
          costAtPurchase: item.costAtPurchase.toNumber(),
          purchaseId: item.purchaseId,
          productId: item.productId,
          discountPercentage: item.discountPercentage
            ? item.discountPercentage.toNumber()
            : null,
          discountAmount: item.discountAmount
            ? item.discountAmount.toNumber()
            : null,
          unit: item.unit,
          tax: item.tax,
          createdAt: item.createdAt.toISOString(),
          updatedAt: item.updatedAt.toISOString(),
          product: {
            name: item.product.name,
          },
        })),
      };
      return plainPurchase;
    });

    return { purchases: serializedPurchases };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mengambil data pembelian." };
  }
};

/**
 * Fungsi untuk mengambil detail satu pembelian berdasarkan ID atau nomor transaksi.
 * @param {string} id - ID unik atau nomor transaksi dari pembelian.
 * @returns {Promise<{purchase?: object, error?: string}>} - Objek yang berisi data detail pembelian atau pesan error.
 */
export const getPurchaseById = async (id: string) => {
  // Dapatkan ID pengguna yang efektif.
  const effectiveUserId = await getEffectiveUserId();
  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    let purchase;
    // Coba cari berdasarkan nomor transaksi terlebih dahulu (tidak case-sensitive).
    purchase = await db.purchase.findFirst({
      where: {
        transactionNumber: {
          mode: "insensitive",
          equals: id,
        },
        userId,
      },
      include: {
        items: { include: { product: true } },
        supplier: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            address: true,
          },
        },
        warehouse: true,
        user: {
          select: {
            id: true,
            name: true,
            businessInfo: {
              select: {
                companyName: true,
                companyAddress: true,
                companyPhone: true,
                companyEmail: true,
              },
            },
          },
        },
      },
    });

    // Jika tidak ditemukan, coba cari berdasarkan ID dengan prefix "pur-".
    if (!purchase && id.toLowerCase().startsWith("pur-")) {
      purchase = await db.purchase.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId,
        },
        include: {
          items: { include: { product: true } },
          supplier: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
              address: true,
            },
          },
          warehouse: true,
          user: {
            select: {
              id: true,
              name: true,
              businessInfo: {
                select: {
                  companyName: true,
                  companyAddress: true,
                  companyPhone: true,
                  companyEmail: true,
                },
              },
            },
          },
        },
      });
    }

    // Jika masih tidak ditemukan, cari berdasarkan ID eksak.
    if (!purchase) {
      purchase = await db.purchase.findUnique({
        where: { id, userId },
        include: {
          items: { include: { product: true } },
          supplier: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
              address: true,
            },
          },
          warehouse: true,
          user: {
            select: {
              id: true,
              name: true,
              businessInfo: {
                select: {
                  companyName: true,
                  companyAddress: true,
                  companyPhone: true,
                  companyEmail: true,
                },
              },
            },
          },
        },
      });
    }

    if (!purchase) {
      return { error: "Pembelian tidak ditemukan." };
    }

    // Serialisasi data untuk client.
    const serializedPurchase = {
      ...purchase,
      totalAmount: Number(purchase.totalAmount),
      purchaseDate: purchase.purchaseDate.toISOString(),
      createdAt: purchase.createdAt.toISOString(),
      updatedAt: purchase.updatedAt.toISOString(),
      transactionDate: purchase.transactionDate?.toISOString(),
      paymentDueDate: purchase.paymentDueDate?.toISOString(),
      memo: purchase.memo || "",
      lampiran: purchase.lampiran || [],
      user: purchase.user
        ? { id: purchase.user.id, name: purchase.user.name }
        : null,
      companyInfo: purchase.user?.businessInfo
        ? { ...purchase.user.businessInfo }
        : null,
      supplier: purchase.supplier ? { ...purchase.supplier } : null,
      items: purchase.items.map((item) => ({
        ...item,
        costAtPurchase: Number(item.costAtPurchase),
        discountPercentage: item.discountPercentage
          ? Number(item.discountPercentage)
          : null,
        discountAmount: item.discountAmount
          ? Number(item.discountAmount)
          : null,
        createdAt: item.createdAt.toISOString(),
        updatedAt: item.updatedAt.toISOString(),
        product: {
          ...item.product,
          price: Number(item.product.price),
          cost: item.product.cost ? Number(item.product.cost) : null,
          createdAt: item.product.createdAt.toISOString(),
          updatedAt: item.product.updatedAt.toISOString(),
          weight: item.product.weight ? Number(item.product.weight) : null,
          length: item.product.length ? Number(item.product.length) : null,
          width: item.product.width ? Number(item.product.width) : null,
          height: item.product.height ? Number(item.product.height) : null,
          wholesalePrice: item.product.wholesalePrice
            ? Number(item.product.wholesalePrice)
            : null,
          salePriceTaxRate: item.product.salePriceTaxRate
            ? Number(item.product.salePriceTaxRate)
            : null,
          wholesalePriceTaxRate: item.product.wholesalePriceTaxRate
            ? Number(item.product.wholesalePriceTaxRate)
            : null,
          costPriceTaxRate: item.product.costPriceTaxRate
            ? Number(item.product.costPriceTaxRate)
            : null,
        },
      })),
    };

    return { purchase: serializedPurchase };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mengambil detail pembelian." };
  }
};

/**
 * Fungsi untuk memperbarui data pembelian yang sudah ada.
 * @param {string} id - ID atau nomor transaksi dari pembelian yang akan diperbarui.
 * @param {z.infer<typeof PurchaseSchema>} values - Data pembelian baru yang telah divalidasi.
 * @returns {Promise<{success?: string, error?: string, data?: object}>} - Objek yang berisi pesan sukses atau error, dan data pembelian yang diperbarui.
 */
export const updatePurchase = async (
  id: string,
  values: z.infer<typeof PurchaseSchema>
) => {
  // Dapatkan ID pengguna yang efektif.
  const effectiveUserId = await getEffectiveUserId();
  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validasi input di sisi server.
  const validatedFields = PurchaseSchema.safeParse(values);
  if (!validatedFields.success) {
    return { error: "Input tidak valid!" };
  }

  const {
    items,
    totalAmount,
    invoiceRef,
    supplierId: rawSupplierId,
    isDraft,
    supplierEmail,
    transactionDate,
    paymentDueDate,
    transactionNumber,
    tags,
    billingAddress,
    warehouseId,
    memo,
    lampiran,
  } = validatedFields.data;

  const supplierId = rawSupplierId ? rawSupplierId : null;

  try {
    // Cari pembelian yang ada berdasarkan ID atau nomor transaksi.
    let existingPurchase = await db.purchase.findFirst({
      where: { transactionNumber: { mode: "insensitive", equals: id }, userId },
      include: { items: true },
    });
    if (!existingPurchase && id.toLowerCase().startsWith("pur-")) {
      existingPurchase = await db.purchase.findFirst({
        where: { id: { mode: "insensitive", equals: id }, userId },
        include: { items: true },
      });
    }
    if (!existingPurchase) {
      existingPurchase = await db.purchase.findUnique({
        where: { id, userId },
        include: { items: true },
      });
    }

    if (!existingPurchase) {
      return { error: "Pembelian tidak ditemukan!" };
    }

    const originalItems = existingPurchase.items;

    // 2. Perbarui pembelian dalam transaksi database.
    await db.$transaction(async (tx) => {
      // Hapus item lama terlebih dahulu.
      await tx.purchaseItem.deleteMany({
        where: { purchaseId: existingPurchase.id },
      });

      // Perbarui record pembelian dengan data baru.
      const purchase = await tx.purchase.update({
        where: { id: existingPurchase.id },
        data: {
          totalAmount,
          invoiceRef,
          supplierId,
          isDraft: isDraft || false,
          supplierEmail,
          transactionDate,
          paymentDueDate,
          transactionNumber,
          tags: tags || [],
          billingAddress,
          warehouseId,
          memo: memo || "",
          lampiran: lampiran || [],
          items: {
            create: items.map((item) => ({
              quantity: item.quantity,
              costAtPurchase: item.costAtPurchase,
              productId: item.productId,
              unit: item.unit || "Buah",
              tax: item.tax || null,
              discountPercentage: item.discountPercentage || null,
              discountAmount: item.discountAmount || null,
            })),
          },
        },
        include: { items: true },
      });

      // Logika penyesuaian stok yang kompleks.
      // Jika bukan draft (baik sebelum maupun sesudah), sesuaikan stok.
      if (!existingPurchase.isDraft && !isDraft) {
        // Kurangi stok berdasarkan item lama.
        for (const item of originalItems) {
          await tx.product.update({
            where: { id: item.productId },
            data: { stock: { decrement: item.quantity } },
          });
          if (existingPurchase.warehouseId) {
            const existingWarehouseStock = await tx.warehouseStock.findUnique({
              where: {
                productId_warehouseId: {
                  productId: item.productId,
                  warehouseId: existingPurchase.warehouseId,
                },
              },
            });
            if (existingWarehouseStock) {
              await tx.warehouseStock.update({
                where: {
                  productId_warehouseId: {
                    productId: item.productId,
                    warehouseId: existingPurchase.warehouseId,
                  },
                },
                data: {
                  quantity: Math.max(
                    0,
                    existingWarehouseStock.quantity - item.quantity
                  ),
                },
              });
              await tx.stockMovement.create({
                data: {
                  type: StockMovementType.ADJUSTMENT,
                  quantity: -item.quantity,
                  previousStock: existingWarehouseStock.quantity,
                  newStock: Math.max(
                    0,
                    existingWarehouseStock.quantity - item.quantity
                  ),
                  reference: purchase.id,
                  notes: `Purchase update reversal: ${purchase.transactionNumber || purchase.id}`,
                  productId: item.productId,
                  warehouseId: existingPurchase.warehouseId,
                  userId,
                },
              });
            }
          }
        }
        // Tambah stok berdasarkan item baru.
        for (const item of items) {
          await tx.product.update({
            where: { id: item.productId },
            data: { stock: { increment: item.quantity } },
          });
          if (warehouseId) {
            const existingWarehouseStock = await tx.warehouseStock.findUnique({
              where: {
                productId_warehouseId: {
                  productId: item.productId,
                  warehouseId: warehouseId,
                },
              },
            });
            if (existingWarehouseStock) {
              await tx.warehouseStock.update({
                where: {
                  productId_warehouseId: {
                    productId: item.productId,
                    warehouseId: warehouseId,
                  },
                },
                data: { quantity: { increment: item.quantity } },
              });
            } else {
              await tx.warehouseStock.create({
                data: {
                  productId: item.productId,
                  warehouseId: warehouseId,
                  quantity: item.quantity,
                  minLevel: 0,
                },
              });
            }
            await tx.stockMovement.create({
              data: {
                type: StockMovementType.PURCHASE,
                quantity: item.quantity,
                previousStock: existingWarehouseStock?.quantity || 0,
                newStock:
                  (existingWarehouseStock?.quantity || 0) + item.quantity,
                reference: purchase.id,
                notes: `Purchase update: ${purchase.transactionNumber || purchase.id}`,
                productId: item.productId,
                warehouseId: warehouseId,
                userId,
              },
            });
          }
        }
      }
      // Jika dari draft menjadi non-draft, tambah stok.
      else if (existingPurchase.isDraft && !isDraft) {
        for (const item of items) {
          await tx.product.update({
            where: { id: item.productId },
            data: { stock: { increment: item.quantity } },
          });
          if (warehouseId) {
            // ... (logika penambahan stok)
          }
        }
      }
      // Jika dari non-draft menjadi draft, kurangi stok.
      else if (!existingPurchase.isDraft && isDraft) {
        for (const item of originalItems) {
          await tx.product.update({
            where: { id: item.productId },
            data: { stock: { decrement: item.quantity } },
          });
          if (existingPurchase.warehouseId) {
            // ... (logika pengurangan stok)
          }
        }
      }
      return purchase;
    });

    // 3. Revalidasi cache.
    revalidatePath("/dashboard/purchases");
    revalidatePath(`/dashboard/purchases/detail/${id}`);
    revalidatePath(`/dashboard/purchases/edit/${id}`);
    if (existingPurchase.transactionNumber) {
      revalidatePath(
        `/dashboard/purchases/detail/${existingPurchase.transactionNumber}`
      );
      revalidatePath(
        `/dashboard/purchases/edit/${existingPurchase.transactionNumber}`
      );
    }

    // Ambil data yang diperbarui dan serialisasi.
    const updatedPurchase = await db.purchase.findUnique({
      where: { id: existingPurchase.id },
      include: { items: true },
    });
    if (!updatedPurchase) {
      return { error: "Gagal mengambil data pembelian yang diperbarui." };
    }
    const serializedResult = {
      // ... (serialisasi data)
    };

    return {
      success: "Pembelian berhasil diperbarui!",
      data: serializedResult,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal memperbarui pembelian. Silakan coba lagi." };
  }
};

/**
 * Fungsi untuk menghapus data pembelian.
 * @param {string} id - ID atau nomor transaksi dari pembelian yang akan dihapus.
 * @returns {Promise<{success?: string, error?: string}>} - Objek yang berisi pesan sukses atau error.
 */
export const deletePurchase = async (id: string) => {
  // Dapatkan ID pengguna yang efektif.
  const effectiveUserId = await getEffectiveUserId();
  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // Cari pembelian yang ada.
    let existingPurchase = await db.purchase.findFirst({
      where: { transactionNumber: { mode: "insensitive", equals: id }, userId },
      include: { items: true },
    });
    if (!existingPurchase && id.toLowerCase().startsWith("pur-")) {
      existingPurchase = await db.purchase.findFirst({
        where: { id: { mode: "insensitive", equals: id }, userId },
        include: { items: true },
      });
    }
    if (!existingPurchase) {
      existingPurchase = await db.purchase.findUnique({
        where: { id, userId },
        include: { items: true },
      });
    }

    if (!existingPurchase) {
      return { error: "Pembelian tidak ditemukan!" };
    }

    const originalItems = existingPurchase.items;

    // Gunakan transaksi untuk menghapus dan mengembalikan stok.
    await db.$transaction(async (tx) => {
      // Kembalikan stok jika pembelian bukan draft.
      if (!existingPurchase.isDraft) {
        for (const item of originalItems) {
          await tx.product.update({
            where: { id: item.productId },
            data: { stock: { decrement: item.quantity } },
          });
          if (existingPurchase.warehouseId) {
            const existingWarehouseStock = await tx.warehouseStock.findUnique({
              where: {
                productId_warehouseId: {
                  productId: item.productId,
                  warehouseId: existingPurchase.warehouseId,
                },
              },
            });
            if (existingWarehouseStock) {
              await tx.warehouseStock.update({
                where: {
                  productId_warehouseId: {
                    productId: item.productId,
                    warehouseId: existingPurchase.warehouseId,
                  },
                },
                data: {
                  quantity: Math.max(
                    0,
                    existingWarehouseStock.quantity - item.quantity
                  ),
                },
              });
              await tx.stockMovement.create({
                data: {
                  type: StockMovementType.ADJUSTMENT,
                  quantity: -item.quantity,
                  previousStock: existingWarehouseStock.quantity,
                  newStock: Math.max(
                    0,
                    existingWarehouseStock.quantity - item.quantity
                  ),
                  reference: existingPurchase.id,
                  notes: `Purchase deletion reversal: ${existingPurchase.transactionNumber || existingPurchase.id}`,
                  productId: item.productId,
                  warehouseId: existingPurchase.warehouseId,
                  userId,
                },
              });
            }
          }
        }
      }

      // Hapus item pembelian.
      await tx.purchaseItem.deleteMany({
        where: { purchaseId: existingPurchase.id },
      });

      // Hapus pembelian itu sendiri.
      await tx.purchase.delete({
        where: { id: existingPurchase.id, userId },
      });
    });

    // Revalidasi cache.
    revalidatePath("/dashboard/purchases");

    return { success: "Pembelian berhasil dihapus!" };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menghapus pembelian. Silakan coba lagi." };
  }
};

/**
 * Fungsi untuk memperbarui status pembayaran sebuah pembelian.
 * @param {string} id - ID pembelian.
 * @param {"LUNAS" | "BELUM_LUNAS"} status - Status pembayaran baru.
 * @returns {Promise<{success?: string, error?: string, purchase?: object}>} - Objek yang berisi pesan sukses/error dan data pembelian yang diperbarui.
 */
export const updatePurchaseStatus = async (
  id: string,
  status: "LUNAS" | "BELUM_LUNAS"
) => {
  // Dapatkan ID pengguna yang efektif.
  const effectiveUserId = await getEffectiveUserId();
  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }

  try {
    // Perbarui status pembelian di database.
    const purchase = await db.purchase.update({
      where: {
        id,
        userId: effectiveUserId,
      },
      data: {
        status: status as any,
      },
    });

    // Revalidasi cache.
    revalidatePath("/dashboard/purchases");
    revalidatePath(
      `/dashboard/purchases/detail/${purchase.transactionNumber || purchase.id}`
    );

    return {
      success: `Status pembelian berhasil diubah menjadi ${status === "LUNAS" ? "Lunas" : "Belum Lunas"}!`,
      purchase: {
        id: purchase.id,
        status: purchase.status,
      },
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mengubah status pembelian." };
  }
};
