"use server";

import { auth } from "@/lib/auth";
import { db } from "@/lib/prisma";
import { uploadToS3 } from "@/lib/s3";

/**
 * Re-upload Google profile picture to S3 for the current user
 */
export const reuploadGoogleProfilePicture = async () => {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { error: "Tidak terautentikasi!" };
    }

    // Get current user data
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        image: true,
        provider: true,
      },
    });

    if (!user) {
      return { error: "Pengguna tidak ditemukan!" };
    }

    // Check if user has a Google profile picture that's not from our S3 bucket
    if (
      !user.image ||
      !user.image.includes("googleusercontent.com") ||
      user.image.includes(process.env.AWS_S3_BUCKET_NAME || "")
    ) {
      return { error: "Tidak ada foto profil Google yang perlu diupload ulang." };
    }

    console.log(`🖼️ Re-uploading Google profile picture for user ${user.id}`);

    // Fetch the Google profile picture
    const response = await fetch(user.image);
    if (!response.ok) {
      return { error: `Gagal mengambil foto profil: ${response.status} ${response.statusText}` };
    }

    const blob = await response.blob();
    const file = new File([blob], "profile-picture.jpg", {
      type: blob.type || "image/jpeg",
    });

    // Upload to S3
    const uploadResult = await uploadToS3(file, user.id, "profile-pictures");

    if (!uploadResult.success || !uploadResult.url) {
      return { error: uploadResult.error || "Gagal mengupload foto profil ke S3." };
    }

    // Update user's image URL in database
    await db.user.update({
      where: { id: user.id },
      data: { image: uploadResult.url },
    });

    console.log(`✅ Successfully re-uploaded profile picture to S3: ${uploadResult.url}`);

    return {
      success: "Foto profil berhasil diupload ulang ke S3!",
      url: uploadResult.url,
    };
  } catch (error) {
    console.error("❌ Error re-uploading Google profile picture:", error);
    return { error: "Terjadi kesalahan saat mengupload ulang foto profil." };
  }
};

/**
 * Get all Google users who need their profile pictures uploaded to S3
 * (Admin function - for debugging/maintenance)
 */
export const getGoogleUsersNeedingProfileUpload = async () => {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { error: "Tidak terautentikasi!" };
    }

    // Check if user is admin/owner
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== "OWNER") {
      return { error: "Akses ditolak!" };
    }

    // Find Google users with profile pictures not in our S3 bucket
    const googleUsers = await db.user.findMany({
      where: {
        provider: "google",
        image: {
          not: null,
          contains: "googleusercontent.com",
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
      },
    });

    // Filter out users who already have S3 URLs
    const usersNeedingUpload = googleUsers.filter(
      (user) => !user.image?.includes(process.env.AWS_S3_BUCKET_NAME || "")
    );

    return {
      success: true,
      users: usersNeedingUpload,
      count: usersNeedingUpload.length,
    };
  } catch (error) {
    console.error("❌ Error getting Google users needing profile upload:", error);
    return { error: "Terjadi kesalahan saat mengambil data pengguna." };
  }
};

/**
 * Batch upload profile pictures for all Google users
 * (Admin function - for maintenance)
 */
export const batchUploadGoogleProfilePictures = async () => {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { error: "Tidak terautentikasi!" };
    }

    // Check if user is admin/owner
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== "OWNER") {
      return { error: "Akses ditolak!" };
    }

    // Get users needing upload
    const result = await getGoogleUsersNeedingProfileUpload();
    if (result.error || !result.users) {
      return result;
    }

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const googleUser of result.users) {
      try {
        if (!googleUser.image) continue;

        console.log(`🖼️ Uploading profile picture for user ${googleUser.id} (${googleUser.email})`);

        const response = await fetch(googleUser.image);
        if (!response.ok) {
          results.push({
            userId: googleUser.id,
            email: googleUser.email,
            success: false,
            error: `Failed to fetch: ${response.status}`,
          });
          errorCount++;
          continue;
        }

        const blob = await response.blob();
        const file = new File([blob], "profile-picture.jpg", {
          type: blob.type || "image/jpeg",
        });

        const uploadResult = await uploadToS3(file, googleUser.id, "profile-pictures");

        if (uploadResult.success && uploadResult.url) {
          await db.user.update({
            where: { id: googleUser.id },
            data: { image: uploadResult.url },
          });

          results.push({
            userId: googleUser.id,
            email: googleUser.email,
            success: true,
            newUrl: uploadResult.url,
          });
          successCount++;
        } else {
          results.push({
            userId: googleUser.id,
            email: googleUser.email,
            success: false,
            error: uploadResult.error,
          });
          errorCount++;
        }
      } catch (error) {
        results.push({
          userId: googleUser.id,
          email: googleUser.email,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
        errorCount++;
      }
    }

    return {
      success: true,
      message: `Batch upload completed: ${successCount} success, ${errorCount} errors`,
      results,
      successCount,
      errorCount,
    };
  } catch (error) {
    console.error("❌ Error in batch upload:", error);
    return { error: "Terjadi kesalahan saat batch upload." };
  }
};
