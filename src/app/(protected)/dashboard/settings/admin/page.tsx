import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SettingsLayout from "@/components/pages/dashboard/settings/settings-layout";
import { Role } from "@prisma/client";
import { ProtectedRoute } from "@/components/auth/protected-route";
import AdminToolsSettings from "@/components/pages/dashboard/settings/admin/admin-tools-settings";

const AdminToolsSettingsPage = async () => {
  return (
    <ProtectedRoute allowedRoles={[Role.OWNER]}>
      <DashboardLayout>
        <SettingsLayout>
          <AdminToolsSettings />
        </SettingsLayout>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default AdminToolsSettingsPage;
