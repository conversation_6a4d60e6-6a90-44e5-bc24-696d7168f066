import React from "react";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SupplierEditPage from "@/components/pages/dashboard/contacts/suppliers/edit/supplier-edit";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getSupplierById } from "@/actions/entities/suppliers";

export const metadata: Metadata = {
  title: "Edit Supplier | KivaPOS",
  description: "Edit informasi supplier",
};

type Props = {
  params: Promise<{ id: string }>;
};

// This is an async Server Component
export default async function SupplierEdit(props: Props) {
  // Get the id from params (which is now a Promise)
  const params = await props.params;
  const id = params.id;

  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch supplier data
  const supplierResult = await getSupplierById(id);

  if (supplierResult.error || !supplierResult.supplier) {
    notFound();
  }

  return (
    <DashboardLayout>
      <SupplierEditPage supplier={supplierResult.supplier} />
    </DashboardLayout>
  );
}
