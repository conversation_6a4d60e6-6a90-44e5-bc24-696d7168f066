"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Loader2, Upload, Users, RefreshCw } from "lucide-react";
import {
  reuploadGoogleProfilePicture,
  getGoogleUsersNeedingProfileUpload,
  batchUploadGoogleProfilePictures,
} from "@/actions/auth/profile-picture";

interface GoogleUser {
  id: string;
  name: string | null;
  email: string | null;
  image: string | null;
}

interface BatchResult {
  userId: string;
  email: string | null;
  success: boolean;
  newUrl?: string;
  error?: string;
}

export const GoogleProfilePictureManager: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isBatchLoading, setIsBatchLoading] = useState(false);
  const [isCheckingUsers, setIsCheckingUsers] = useState(false);
  const [usersNeedingUpload, setUsersNeedingUpload] = useState<GoogleUser[]>(
    []
  );
  const [batchResults, setBatchResults] = useState<BatchResult[]>([]);

  const handleReuploadCurrentUser = async () => {
    setIsLoading(true);
    try {
      const result = await reuploadGoogleProfilePicture();
      if ("error" in result) {
        toast.error(result.error);
      } else {
        toast.success(result.success || "Foto profil berhasil diupload ulang!");
      }
    } catch (error) {
      toast.error("Terjadi kesalahan saat mengupload ulang foto profil.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCheckUsers = async () => {
    setIsCheckingUsers(true);
    try {
      const result = await getGoogleUsersNeedingProfileUpload();
      if (result.error) {
        toast.error(result.error);
      } else {
        setUsersNeedingUpload(result.users || []);
        toast.success(
          `Ditemukan ${result.count} pengguna yang perlu upload foto profil.`
        );
      }
    } catch (error) {
      toast.error("Terjadi kesalahan saat mengecek pengguna.");
    } finally {
      setIsCheckingUsers(false);
    }
  };

  const handleBatchUpload = async () => {
    setIsBatchLoading(true);
    setBatchResults([]);
    try {
      const result = await batchUploadGoogleProfilePictures();
      if ("error" in result) {
        toast.error(result.error);
      } else {
        setBatchResults(result.results || []);
        toast.success(result.message || "Batch upload selesai!");
        // Refresh the users list
        await handleCheckUsers();
      }
    } catch (error) {
      toast.error("Terjadi kesalahan saat batch upload.");
    } finally {
      setIsBatchLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Google Profile Picture Manager
          </CardTitle>
          <CardDescription>
            Kelola upload foto profil dari Google ke S3 bucket untuk performa
            yang lebih baik.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current User Section */}
          <div className="space-y-2">
            <h4 className="font-medium">Upload Ulang Foto Profil Anda</h4>
            <p className="text-sm text-muted-foreground">
              Jika Anda login dengan Google dan foto profil tidak muncul dengan
              benar, gunakan tombol ini untuk mengupload ulang ke S3.
            </p>
            <Button
              onClick={handleReuploadCurrentUser}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Upload Ulang Foto Profil
            </Button>
          </div>

          {/* Admin Section */}
          <div className="border-t pt-4 space-y-4">
            <h4 className="font-medium">Admin Tools</h4>

            {/* Check Users */}
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Cek pengguna Google yang perlu upload foto profil ke S3.
              </p>
              <Button
                onClick={handleCheckUsers}
                disabled={isCheckingUsers}
                variant="outline"
                size="sm"
              >
                {isCheckingUsers ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Users className="h-4 w-4 mr-2" />
                )}
                Cek Pengguna
              </Button>
            </div>

            {/* Users List */}
            {usersNeedingUpload.length > 0 && (
              <div className="space-y-2">
                <h5 className="font-medium text-sm">
                  Pengguna yang perlu upload ({usersNeedingUpload.length})
                </h5>
                <div className="max-h-40 overflow-y-auto space-y-1">
                  {usersNeedingUpload.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-2 bg-muted rounded text-sm"
                    >
                      <div>
                        <span className="font-medium">
                          {user.name || "No Name"}
                        </span>
                        <span className="text-muted-foreground ml-2">
                          ({user.email})
                        </span>
                      </div>
                      <Badge variant="secondary">Google</Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Batch Upload */}
            {usersNeedingUpload.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Upload semua foto profil Google ke S3 sekaligus.
                </p>
                <Button
                  onClick={handleBatchUpload}
                  disabled={isBatchLoading}
                  variant="default"
                  size="sm"
                >
                  {isBatchLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Upload className="h-4 w-4 mr-2" />
                  )}
                  Batch Upload ({usersNeedingUpload.length} pengguna)
                </Button>
              </div>
            )}

            {/* Batch Results */}
            {batchResults.length > 0 && (
              <div className="space-y-2">
                <h5 className="font-medium text-sm">Hasil Batch Upload</h5>
                <div className="max-h-40 overflow-y-auto space-y-1">
                  {batchResults.map((result, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-muted rounded text-sm"
                    >
                      <div>
                        <span className="font-medium">{result.email}</span>
                        {result.error && (
                          <span className="text-red-500 ml-2 text-xs">
                            {result.error}
                          </span>
                        )}
                      </div>
                      <Badge
                        variant={result.success ? "default" : "destructive"}
                      >
                        {result.success ? "Success" : "Error"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
