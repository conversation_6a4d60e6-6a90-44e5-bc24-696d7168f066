import { ColumnVisibility } from "../types";

export interface ColumnConfig {
  key: keyof ColumnVisibility;
  label: string;
  sortKey: string;
}

// Customers column configuration with order (matching the actual table structure)
export const customersColumnConfig: ColumnConfig[] = [
  { key: "name", label: "<PERSON><PERSON>", sortKey: "name" },
  { key: "contactName", label: "Nam<PERSON>k", sortKey: "contactName" },
  { key: "email", label: "Email", sortKey: "email" },
  { key: "phone", label: "Telepon", sortKey: "phone" },
  { key: "address", label: "<PERSON>ama<PERSON>", sortKey: "address" },
  { key: "NIK", label: "NIK", sortKey: "NIK" },
  { key: "NPWP", label: "NPWP", sortKey: "NPWP" },
  { key: "createdAt", label: "Tanggal Dibuat", sortKey: "createdAt" },
  { key: "updatedAt", label: "<PERSON><PERSON>", sortKey: "updatedAt" },
  { key: "notes", label: "Catatan", sortKey: "notes" },
];
