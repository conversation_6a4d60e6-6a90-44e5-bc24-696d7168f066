import React from "react";
import { SupplierStatus } from "../types";
import { Building2, CheckCircle2, XCircle } from "lucide-react";

interface SupplierStatusCardsProps {
  supplierStatus: SupplierStatus;
}

export const SupplierStatusCards: React.FC<SupplierStatusCardsProps> = ({
  supplierStatus,
}) => {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {/* Total Suppliers */}
      <div className="rounded-lg border border-gray-200 bg-blue-50 dark:border-gray-700 dark:bg-blue-900/20">
        <div className="flex justify-between items-center p-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Total Supplier
          </div>
          <Building2 className="h-5 w-5 text-blue-500 dark:text-blue-400" />
        </div>
        <div className="px-4 pb-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Total supplier
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {supplierStatus.total}
          </div>
        </div>
      </div>

      {/* Active Suppliers */}
      <div className="rounded-lg border border-gray-200 bg-green-50 dark:border-gray-700 dark:bg-green-900/20">
        <div className="flex justify-between items-center p-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Supplier Aktif
          </div>
          <CheckCircle2 className="h-5 w-5 text-green-500 dark:text-green-400" />
        </div>
        <div className="px-4 pb-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Aktif bertransaksi
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {supplierStatus.active}
          </div>
        </div>
      </div>

      {/* Inactive Suppliers */}
      <div className="rounded-lg border border-gray-200 bg-red-50 dark:border-gray-700 dark:bg-red-900/20">
        <div className="flex justify-between items-center p-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Supplier Tidak Aktif
          </div>
          <XCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
        </div>
        <div className="px-4 pb-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Tidak aktif bertransaksi
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {supplierStatus.inactive}
          </div>
        </div>
      </div>
    </div>
  );
};
