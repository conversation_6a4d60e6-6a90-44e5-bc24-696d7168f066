import { ColumnVisibility } from "../types";

export interface ColumnConfig {
  key: keyof ColumnVisibility;
  label: string;
  sortKey: string;
}

// Suppliers column configuration with order (matching the actual table structure)
export const suppliersColumnConfig: ColumnConfig[] = [
  { key: "name", label: "Nama Supplier", sortKey: "name" },
  { key: "contactName", label: "Nama Kontak", sortKey: "contactName" },
  { key: "email", label: "Email", sortKey: "email" },
  { key: "phone", label: "Telepon", sortKey: "phone" },
  { key: "address", label: "Alama<PERSON>", sortKey: "address" },
  { key: "createdAt", label: "Tanggal Dibuat", sortKey: "createdAt" },
  { key: "updatedAt", label: "Tanggal Diperbarui", sortKey: "updatedAt" },
  { key: "notes", label: "Catatan", sortKey: "notes" },
];
