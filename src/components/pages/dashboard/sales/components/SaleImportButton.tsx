"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  CheckCircle,
  Info,
  Calendar,
  Settings,
  FileSpreadsheet,
  AlertCircle, // Added for conflict dialog
  FileX, // Added for conflict dialog
  XCircle, // Added for error display
  Clock, // Added for skipped rows
  Package, // Added for new suppliers/products (though not directly used for sales, good to keep consistent if logic changes)
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import { createSalesImportTemplate } from "@/utils/imports/salesImportTemplate";
import {
  importSales,
  importSalesWithConflictResolution,
} from "@/actions/import/salesImport";

interface ImportSummary {
  salesCreated: number;
  customersCreated: number;
  errors: string[];
  skippedRows?: number; // Added for conflict resolution
  conflictedInvoices?: string[]; // Added for conflict resolution
}

interface ConflictDetails {
  duplicateInvoices: string[];
  affectedRows: number[]; // Rows in the Excel that are conflicted
  validRowsCount: number;
  totalRowsCount: number;
}

interface ImportResult {
  success?: string;
  error?: string;
  summary: ImportSummary;
  hasConflicts?: boolean; // Added for conflict resolution
  conflictDetails?: ConflictDetails; // Added for conflict resolution
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  includeSummary: boolean;
  includeCharts: boolean;
  importFriendly: boolean; // New option for import-friendly format
}

interface SalesImportExportProps {
  onRefresh?: () => void;
}

export const SaleImportButton: React.FC<SalesImportExportProps> = ({
  onRefresh,
}) => {
  // Import states
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showConflictDialog, setShowConflictDialog] = useState(false); // New state for conflict dialog
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(
    null
  );
  const [conflictDetails, setConflictDetails] =
    useState<ConflictDetails | null>(null); // New state for conflict details
  const [pendingFileBuffer, setPendingFileBuffer] =
    useState<ArrayBuffer | null>(null); // New state to hold file buffer for conflict resolution
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Download template function
  const downloadTemplate = () => {
    try {
      const workbook = createSalesImportTemplate();
      const fileName = `template-import-penjualan-${
        new Date().toISOString().split("T")[0]
      }.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template");
    }
  };

  // Reset import states
  const resetImportState = () => {
    setIsImporting(false);
    setImportProgress(0);
    setImportSummary(null);
    setConflictDetails(null);
    setPendingFileBuffer(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle import with conflicts
  const handleImportWithConflicts = async () => {
    if (!pendingFileBuffer) return;

    setShowConflictDialog(false); // Close conflict dialog
    setIsImporting(true);
    setImportProgress(0);

    try {
      setImportProgress(20);
      // Call the specific function for conflict resolution
      const result: ImportResult =
        await importSalesWithConflictResolution(pendingFileBuffer);
      setImportProgress(80);

      if (result.success) {
        setImportProgress(100);
        setImportSummary(result.summary);

        // Create detailed success message, similar to PurchaseImportButton
        let successMessage = result.success;
        if (
          result.summary.conflictedInvoices &&
          result.summary.conflictedInvoices.length > 0
        ) {
          successMessage += ` ${result.summary.conflictedInvoices.length} invoice yang bertentangan diabaikan.`;
        }
        toast.success(successMessage + " Lihat detail di bawah.");

        if (onRefresh) {
          setTimeout(() => {
            onRefresh();
          }, 1500);
        }
      } else if (result.error) {
        setImportSummary(result.summary);
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Import with conflict resolution error:", error);
      toast.error(
        "Gagal mengimpor file dengan penyelesaian konflik atau tidak valid"
      );
      setImportSummary({
        salesCreated: 0,
        customersCreated: 0,
        errors: ["Gagal memproses file"],
        skippedRows: 0,
        conflictedInvoices: [],
      });
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      setPendingFileBuffer(null);
    }
  };

  // Handle import function
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/)) {
      toast.error(
        "Format file tidak didukung. Gunakan file Excel (.xlsx atau .xls)"
      );
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file terlalu besar. Maksimal 10MB");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportSummary(null); // Clear previous summary
    setConflictDetails(null); // Clear previous conflict details
    setPendingFileBuffer(null); // Clear any pending file buffer

    try {
      setImportProgress(20);

      const arrayBuffer = await file.arrayBuffer();
      setImportProgress(40);

      const result: ImportResult = await importSales(arrayBuffer); // Ensure importSales returns ImportResult type
      setImportProgress(80);

      // Check if there are conflicts
      if (result.hasConflicts && result.conflictDetails) {
        setImportProgress(100);
        setConflictDetails(result.conflictDetails);
        setPendingFileBuffer(arrayBuffer); // Store the file buffer for potential re-import with conflicts
        setShowConflictDialog(true); // Open the conflict dialog
        setIsImporting(false); // Stop importing animation
        return; // Exit function, as user needs to resolve conflict
      }

      if (result.success) {
        setImportProgress(100);
        setImportSummary(result.summary || null);
        toast.success(result.success + " Lihat detail di bawah."); // Changed message to be consistent

        // Auto-refresh data on successful import to show newly imported items
        if (onRefresh) {
          setTimeout(() => {
            onRefresh();
          }, 1500); // Small delay to ensure notifications are processed
        }
      } else if (result.error) {
        setImportSummary(result.summary || null);

        // Enhanced error message handling, similar to PurchaseImportButton
        let errorMessage = result.error;
        if (result.summary?.errors && result.summary.errors.length > 0) {
          const firstError = result.summary.errors[0];
          if (
            firstError.includes("Produk") &&
            firstError.includes("tidak ditemukan")
          ) {
            errorMessage =
              "Beberapa produk dalam file tidak ditemukan di sistem. Pastikan nama produk sama persis (case-sensitive).";
          } else if (firstError.includes("case-sensitive")) {
            errorMessage =
              "Nama produk harus sama persis dengan yang ada di sistem (case-sensitive).";
          } else if (firstError.includes("Invoice sudah ada")) {
            // Specific error for sales invoices
            errorMessage = "Beberapa invoice sudah ada di sistem.";
          }
        }
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Import error:", error);
      let errorMessage = "Gagal mengimpor file";
      if (error instanceof Error) {
        if (error.message.includes("timeout")) {
          errorMessage =
            "Import timeout. File terlalu besar atau koneksi lambat. Coba dengan file yang lebih kecil.";
        } else if (error.message.includes("authentication")) {
          errorMessage = "Sesi Anda telah berakhir. Silakan login kembali.";
        } else {
          errorMessage = `Terjadi kesalahan: ${error.message}`; // Generic error message
        }
      }
      toast.error(errorMessage);
      setImportSummary({
        salesCreated: 0,
        customersCreated: 0,
        errors: [errorMessage],
        skippedRows: 0, // Initialize skippedRows
        conflictedInvoices: [], // Initialize conflictedInvoices
      });
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Import Button and Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col rounded-md">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Penjualan
            </DialogTitle>
            <DialogDescription className="flex text-left">
              Import data penjualan dari file Excel dengan mudah dan aman
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Cara Import Data Penjualan:
                  </h4>
                  <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
                    <li>
                      Download template Excel dengan klik tombol &quot;Download
                      Template&quot;
                    </li>
                    <li>Isi data penjualan sesuai format yang tersedia</li>
                    <li>
                      <strong>Pastikan nama produk sama persis</strong> dengan
                      yang ada di sistem
                    </li>{" "}
                    {/* Added important note for sales */}
                    <li>Upload file Excel yang sudah diisi</li>
                    <li>Tunggu proses import selesai</li>
                  </ol>
                </div>
              </div>
            </div>

            {/* Download Template */}
            <div className="space-y-2">
              <h4 className="font-medium">1. Download Template Excel</h4>
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="w-full cursor-pointer"
              >
                <Download className="mr-2 h-4 w-4" />
                Download Template Penjualan
              </Button>
            </div>

            <Separator />

            {/* File Upload */}
            <div className="space-y-2">
              <h4 className="font-medium">2. Upload File Excel</h4>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
                <p className="text-sm text-slate-600 mb-2">
                  Klik untuk memilih file atau drag & drop
                </p>
                <p className="text-xs text-slate-500">
                  Format: .xlsx, .xls (Maksimal 10MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4 cursor-pointer"
                  disabled={isImporting}
                >
                  {isImporting ? "Memproses..." : "Pilih File Excel"}
                </Button>
              </div>
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress Import</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} className="w-full" />
              </div>
            )}

            {/* Import Summary */}
            {importSummary && (
              <div className="space-y-4">
                {" "}
                {/* Changed to space-y-4 for consistency */}
                <h4 className="font-medium">Hasil Import:</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">
                        Penjualan Dibuat
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {importSummary.salesCreated}
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-blue-500" />{" "}
                      {/* Changed icon to Package for consistency with purchases */}
                      <span className="text-sm font-medium">
                        Pelanggan Baru
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {importSummary.customersCreated}
                    </div>
                  </div>
                </div>
                {/* Additional Statistics (Skipped Rows) */}
                {importSummary.skippedRows! > 0 && (
                  <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-orange-500" />
                      <span className="text-sm font-medium">
                        Data Diabaikan
                      </span>
                    </div>
                    <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
                      {importSummary.skippedRows} baris
                    </div>
                    <div className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                      Data tidak valid atau konflik
                    </div>
                  </div>
                )}
                {/* Conflicted Invoices (if any after import with conflicts) */}
                {importSummary.conflictedInvoices &&
                  importSummary.conflictedInvoices.length > 0 && (
                    <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <FileX className="h-4 w-4 text-amber-600" />
                        <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                          Invoice yang Diabaikan
                        </span>
                      </div>
                      <div className="text-xs text-amber-700 dark:text-amber-300 space-y-1">
                        {importSummary.conflictedInvoices
                          .slice(0, 3)
                          .map((invoice, index) => (
                            <div key={index}>• {invoice}</div>
                          ))}
                        {importSummary.conflictedInvoices.length > 3 && (
                          <div>
                            ... dan{" "}
                            {importSummary.conflictedInvoices.length - 3}{" "}
                            lainnya
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                {importSummary.errors.length > 0 && (
                  <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                    <h5 className="font-medium text-red-800 dark:text-red-200 mb-2 flex items-center gap-2">
                      <XCircle className="h-4 w-4" />
                      Error ({importSummary.errors.length}):
                    </h5>{" "}
                    {/* Added XCircle icon */}
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {importSummary.errors.slice(0, 5).map((error, index) => (
                        <div
                          key={index}
                          className="text-sm text-red-700 dark:text-red-300"
                        >
                          • {error}
                        </div>
                      ))}
                      {importSummary.errors.length > 5 && (
                        <div className="text-sm text-red-700 dark:text-red-300">
                          ... dan {importSummary.errors.length - 5} error
                          lainnya
                        </div>
                      )}
                    </div>
                  </div>
                )}
                {/* Summary Text */}
                <div className="bg-slate-50 dark:bg-slate-900/20 p-3 rounded-lg">
                  <h5 className="font-medium text-slate-800 dark:text-slate-200 mb-2">
                    Ringkasan Import:
                  </h5>
                  <div className="text-sm text-slate-700 dark:text-slate-300 space-y-1">
                    <p>
                      ✅ <strong>{importSummary.salesCreated}</strong> penjualan
                      berhasil diimpor
                    </p>
                    <p>
                      👥 <strong>{importSummary.customersCreated}</strong>{" "}
                      pelanggan baru dibuat
                    </p>
                    {importSummary.skippedRows! > 0 && (
                      <p>
                        ⚠️ <strong>{importSummary.skippedRows}</strong> baris
                        diabaikan karena error atau konflik
                      </p>
                    )}
                    {importSummary.errors.length > 0 && (
                      <p>
                        ❌ <strong>{importSummary.errors.length}</strong> error
                        ditemukan dalam proses import
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Conflict Resolution Dialog */}
      <Dialog open={showConflictDialog} onOpenChange={setShowConflictDialog}>
        <DialogContent className="max-w-md rounded-md">
          {" "}
          {/* Added rounded-md */}
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Konflik Data Import
            </DialogTitle>
            <DialogDescription>
              Ditemukan invoice yang sudah ada dalam sistem
            </DialogDescription>
          </DialogHeader>
          {conflictDetails && (
            <div className="space-y-4">
              {/* Conflict Summary */}
              <div className="rounded-lg bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 p-4">
                <div className="flex items-start gap-3">
                  <FileX className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                      Invoice yang Bertentangan
                    </h4>
                    <div className="mt-2 text-sm text-amber-700 dark:text-amber-300">
                      <p>
                        Ditemukan{" "}
                        <strong>
                          {conflictDetails.duplicateInvoices.length}
                        </strong>{" "}
                        invoice yang sudah ada dalam sistem:
                      </p>
                      <div className="mt-2 max-h-20 overflow-y-auto">
                        <ul className="list-disc list-inside space-y-1">
                          {conflictDetails.duplicateInvoices
                            .slice(0, 3)
                            .map((invoice, index) => (
                              <li key={index} className="text-xs">
                                {invoice}
                              </li>
                            ))}
                          {conflictDetails.duplicateInvoices.length > 3 && (
                            <li className="text-xs">
                              ... dan{" "}
                              {conflictDetails.duplicateInvoices.length - 3}{" "}
                              lainnya
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Import Statistics */}
              <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Info className="h-5 w-5 text-blue-600" />
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Statistik Import
                  </h4>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-blue-600 dark:text-blue-400 font-semibold">
                      {conflictDetails.validRowsCount}
                    </div>
                    <div className="text-blue-700 dark:text-blue-300">
                      Data Valid
                    </div>
                  </div>
                  <div>
                    <div className="text-amber-600 dark:text-amber-400 font-semibold">
                      {conflictDetails.affectedRows.length}
                    </div>
                    <div className="text-amber-700 dark:text-amber-300">
                      Tidak Valid
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-sm text-slate-600 dark:text-slate-400">
                <p>
                  Apakah Anda ingin melanjutkan import dengan mengabaikan data
                  yang bertentangan?
                  <strong className="text-slate-800 dark:text-slate-200">
                    {" "}
                    {conflictDetails.validRowsCount}{" "}
                  </strong>
                  data valid akan diimpor.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-2">
                <Button
                  onClick={handleImportWithConflicts}
                  className="flex-1"
                  disabled={isImporting}
                >
                  {isImporting ? "Mengimpor..." : "Ya, Lanjutkan Import"}
                </Button>
                <Button
                  onClick={() => {
                    setShowConflictDialog(false);
                    resetImportState();
                  }}
                  variant="outline"
                  className="flex-1"
                  disabled={isImporting}
                >
                  Batal
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
