"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Info,
  Calendar,
  Settings,
  FileSpreadsheet,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import { getSalesReportDataWithFilters } from "@/actions/reports";
import {
  createSalesExcelReport,
  createSalesImportFriendlyExcelReport,
} from "@/utils/exports/salesExportTemplate";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import {
  generateDateStringForFilename,
  validateExportPeriod,
} from "@/utils/dateUtils";

// Interface for export configuration
interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  includeSummary: boolean;
  includeCharts: boolean; // Not currently used in the provided logic but kept for consistency
  importFriendly: boolean;
}

export const SaleExportButton: React.FC = () => {
  // State for controlling the export dialog visibility
  const [showExportDialog, setShowExportDialog] = useState(false);
  // State for managing the exporting process loading indicator
  const [isExporting, setIsExporting] = useState(false);
  // State for showing the export progress
  const [exportProgress, setExportProgress] = useState(0);
  // State for export configuration, initialized with current month and year
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    includeSummary: false,
    includeCharts: false,
    importFriendly: false,
  });

  // Function to handle the advanced export process
  const handleAdvancedExport = async () => {
    setIsExporting(true); // Set exporting state to true
    setExportProgress(0); // Reset progress

    try {
      // Validate the selected export period (e.g., date range validity)
      const validationError = validateExportPeriod(exportConfig.reportType, {
        selectedDate: exportConfig.selectedDate,
        selectedMonth: exportConfig.selectedMonth,
        selectedYear: exportConfig.selectedYear,
      });

      // If there's a validation error, show a toast and stop the export
      if (validationError) {
        toast.error(validationError);
        return;
      }

      setExportProgress(20); // Update progress

      // Determine the start and end dates based on the report type
      let startDate: Date | undefined;
      let endDate: Date | undefined;
      let dateRange: string = "monthly"; // Default date range
      let periodLabel: string = "Data Penjualan"; // Default period label for the report

      if (exportConfig.reportType === "harian") {
        // For daily reports, set start and end to the selected date
        startDate = new Date(exportConfig.selectedDate);
        startDate.setHours(0, 0, 0, 0); // Set to start of the day
        endDate = new Date(exportConfig.selectedDate);
        endDate.setHours(23, 59, 59, 999); // Set to end of the day
        dateRange = "daily";
        periodLabel = `Harian - ${startDate.toLocaleDateString("id-ID")}`; // Format label for daily
      } else if (exportConfig.reportType === "bulanan") {
        // For monthly reports, get the year and month
        const year = exportConfig.selectedYear || new Date().getFullYear();
        const month = exportConfig.selectedMonth || new Date().getMonth();
        startDate = new Date(year, month, 1); // Set to the first day of the month
        endDate = new Date(year, month + 1, 0, 23, 59, 59, 999); // Set to the last day of the month
        dateRange = "monthly";
        periodLabel = `Bulanan - ${startDate.toLocaleDateString("id-ID", { month: "long", year: "numeric" })}`; // Format label for monthly
      } else if (exportConfig.reportType === "tahunan") {
        // For yearly reports, get the year
        const year = exportConfig.selectedYear || new Date().getFullYear();
        startDate = new Date(year, 0, 1); // Set to the first day of the year
        endDate = new Date(year, 11, 31, 23, 59, 59, 999); // Set to the last day of the year
        dateRange = "yearly";
        periodLabel = `Tahunan - ${year}`; // Format label for yearly
      }

      setExportProgress(40); // Update progress

      // Fetch sales data from the backend with the determined filters
      const salesResult = await getSalesReportDataWithFilters({
        dateRange,
        startDate,
        endDate,
        customer: undefined, // Customer filter is not implemented in this component
      });

      setExportProgress(70); // Update progress

      // Handle any errors from the data fetch
      if (salesResult.error) {
        console.error("Sales data fetch error:", salesResult.error);
        throw new Error(salesResult.error); // Re-throw to be caught by the outer catch block
      }

      // If no data is returned, show an error toast
      if (!salesResult.data || salesResult.data.length === 0) {
        toast.error(
          "Tidak ada data penjualan untuk diekspor pada periode yang dipilih"
        );
        return; // Stop the function execution
      }

      setExportProgress(85); // Update progress

      // Generate the Excel workbook based on the 'importFriendly' flag
      const workbook = exportConfig.importFriendly
        ? createSalesImportFriendlyExcelReport(salesResult.data)
        : createSalesExcelReport(salesResult.data, periodLabel, {
            companyName: "KivaPOS", // Company name for the report header
            reportTitle: "Laporan Penjualan", // Report title
          });

      // Convert the workbook to an ArrayBuffer for download
      const excelBuffer = XLSX.write(workbook, {
        type: "array",
        bookType: "xlsx",
      });

      setExportProgress(100); // Final progress update

      // Generate a date string for the filename
      const dateString = generateDateStringForFilename(
        exportConfig.reportType,
        {
          selectedDate: exportConfig.selectedDate,
          selectedMonth: exportConfig.selectedMonth,
          selectedYear: exportConfig.selectedYear,
        }
      );

      // Construct the filename based on the 'importFriendly' flag and date string
      const fileName = exportConfig.importFriendly
        ? `data-penjualan-import-${dateString}.xlsx`
        : `laporan-penjualan-${exportConfig.reportType}-${dateString}.xlsx`;

      // Create a Blob from the Excel buffer and initiate download
      const blob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a"); // Create a temporary anchor element
      link.href = url; // Set the href to the Blob URL
      link.download = fileName; // Set the download filename
      document.body.appendChild(link); // Append to body (required for Firefox)
      link.click(); // Programmatically click the link to trigger download
      document.body.removeChild(link); // Remove the link after download
      URL.revokeObjectURL(url); // Revoke the Blob URL to free up memory

      toast.success("Export berhasil!"); // Show success notification
      setShowExportDialog(false); // Close the dialog on successful export
    } catch (error) {
      console.error("Export error:", error); // Log any errors
      toast.error("Gagal mengekspor data"); // Show error notification to the user
    } finally {
      setIsExporting(false); // Reset exporting state
      setExportProgress(0); // Reset progress
    }
  };

  return (
    <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-2 cursor-pointer"
        >
          <Download className="h-4 w-4" />
          Export
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[80vh] flex flex-col rounded-md">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Export Data Penjualan
          </DialogTitle>
          <DialogDescription className="flex text-left">
            Pilih periode dan format yang ingin diekspor untuk data penjualan
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 p-2">
          {/* Period Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Periode Laporan</Label>
            <div className="grid grid-cols-3 gap-2">
              {[
                { value: "harian", label: "Harian", icon: Calendar },
                { value: "bulanan", label: "Bulanan", icon: Calendar },
                { value: "tahunan", label: "Tahunan", icon: Calendar },
              ].map((type) => (
                <Card
                  key={type.value}
                  className={`cursor-pointer transition-all hover:shadow-sm ${
                    exportConfig.reportType === type.value
                      ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                      : "hover:bg-gray-50 dark:hover:bg-gray-800"
                  }`}
                  onClick={() =>
                    setExportConfig((prev) => ({
                      ...prev,
                      reportType: type.value as any,
                    }))
                  }
                >
                  <div className="p-3 text-center">
                    <type.icon className="h-5 w-5 mx-auto mb-1" />
                    <div className="text-sm font-medium">{type.label}</div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Date/Period Selection */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Pilih Periode</Label>

            {exportConfig.reportType === "harian" && (
              <div>
                <DatePicker
                  date={exportConfig.selectedDate}
                  setDate={(date) => {
                    setExportConfig((prev) => ({
                      ...prev,
                      selectedDate: date || new Date(),
                    }));
                  }}
                  placeholder="Pilih tanggal"
                  className="w-full h-9 cursor-pointer"
                />
              </div>
            )}

            {exportConfig.reportType === "bulanan" && (
              <div className="grid grid-cols-2 gap-2">
                <Select
                  value={exportConfig.selectedMonth?.toString() || ""}
                  onValueChange={(value) => {
                    setExportConfig((prev) => ({
                      ...prev,
                      selectedMonth: parseInt(value),
                    }));
                  }}
                >
                  <SelectTrigger className="h-9 w-full">
                    <SelectValue placeholder="Bulan" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 12 }, (_, i) => (
                      <SelectItem key={i} value={i.toString()}>
                        {new Date(0, i).toLocaleDateString("id-ID", {
                          month: "long",
                        })}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Input
                  type="number"
                  min="2020"
                  max="2030"
                  placeholder="Tahun"
                  value={exportConfig.selectedYear || ""}
                  onChange={(e) => {
                    setExportConfig((prev) => ({
                      ...prev,
                      selectedYear:
                        parseInt(e.target.value) || new Date().getFullYear(),
                    }));
                  }}
                  className="h-11"
                />
              </div>
            )}

            {exportConfig.reportType === "tahunan" && (
              <div>
                <Input
                  type="number"
                  min="2020"
                  max="2030"
                  placeholder="Tahun"
                  value={exportConfig.selectedYear || ""}
                  onChange={(e) => {
                    setExportConfig((prev) => ({
                      ...prev,
                      selectedYear:
                        parseInt(e.target.value) || new Date().getFullYear(),
                    }));
                  }}
                  className="w-full h-9"
                />
              </div>
            )}
          </div>

          <Separator />

          {/* Format Selection - Excel Only */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Format Export</Label>
            <div className="flex items-center gap-2 p-3 border rounded-md bg-gray-50 dark:bg-gray-800">
              <FileSpreadsheet className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Excel (.xlsx)</span>
              <span className="text-xs text-gray-500 ml-auto">
                Dengan sheet &apos;Data Produk&apos; dan &apos;Info
                Dokumen&apos;
              </span>
            </div>
          </div>

          <Separator />

          {/* Additional Options */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Opsi Tambahan</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="importFriendly"
                  checked={exportConfig.importFriendly}
                  onCheckedChange={(checked) =>
                    setExportConfig((prev) => ({
                      ...prev,
                      importFriendly: checked as boolean,
                    }))
                  }
                />
                <Label htmlFor="importFriendly" className="text-sm">
                  Format untuk Import
                </Label>
                <Info className="h-3 w-3 text-gray-400" />
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeSummary"
                  checked={exportConfig.includeSummary}
                  onCheckedChange={(checked) =>
                    setExportConfig((prev) => ({
                      ...prev,
                      includeSummary: checked as boolean,
                    }))
                  }
                />
                <Label
                  htmlFor="includeSummary"
                  className="text-sm cursor-pointer"
                >
                  Sertakan ringkasan data
                </Label>
              </div>
            </div>
          </div>

          {/* Export Progress */}
          {isExporting && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress Export</span>
                <span>{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} className="w-full" />
            </div>
          )}
        </div>

        {/* Action Buttons - Fixed at bottom */}
        <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
          <Button
            className="cursor-pointer"
            variant="outline"
            onClick={() => setShowExportDialog(false)}
          >
            Batal
          </Button>
          <Button
            onClick={handleAdvancedExport}
            disabled={isExporting}
            className="flex items-center gap-2 cursor-pointer"
          >
            <Download className="h-4 w-4" />
            {isExporting ? "Mengekspor..." : "Export Data Penjualan"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
