"use client";

import React, { useState, useTransition, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import Link from "next/link";
import DashboardLayout from "@/components/layout/dashboardlayout";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { updateService } from "@/actions/entities/services";
import { getProducts } from "@/lib/get-products";
import { EnhancedServiceSchema } from "../new/types";
import { ServiceFormValues, Product } from "../new/types";
import CombinedServiceForm from "../new/components/CombinedServiceForm";
import { ArrowLeft, Check, Save } from "lucide-react";
import EnhancedServiceFormSummary from "../new/components/ServiceFormSummary";
import { Service } from "../types";
import { useUnsavedChangesWarning } from "@/hooks/useUnsavedChangesWarning";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ServiceEditPageProps {
  service: Service;
}

const ServiceEditPage: React.FC<ServiceEditPageProps> = ({ service }) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [products, setProducts] = useState<Product[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [originalValues, setOriginalValues] =
    useState<ServiceFormValues | null>(null);

  // Initialize the form with enhanced schema and service data
  const form = useForm<ServiceFormValues>({
    resolver: zodResolver(EnhancedServiceSchema),
    defaultValues: {
      serviceNumber: service.serviceNumber,
      customerName: service.customerName,
      customerPhone: service.customerPhone,
      customerEmail: service.customerEmail || "",
      deviceType: service.deviceType,
      deviceBrand: service.deviceBrand,
      deviceModel: service.deviceModel,
      deviceSerialNumber: service.deviceSerialNumber || "",
      problemDescription: service.problemDescription,
      estimatedCost: service.estimatedCost || 0,
      finalCost: service.finalCost || 0,
      estimatedCompletionDate: service.estimatedCompletionDate
        ? service.estimatedCompletionDate.split("T")[0]
        : "",
      diagnosisNotes: service.diagnosisNotes || "",
      repairNotes: service.repairNotes || "",
      customerAddress: service.customerAddress || "",
      warrantyPeriod: service.warrantyPeriod || 0,
      priorityLevel: service.priorityLevel || "MEDIUM",
      customerId: service.customerId || "",
      attachments: service.lampiran || [],
      transactionDate: service.receivedDate
        ? new Date(service.receivedDate)
        : new Date(),
      dueDate: service.estimatedCompletionDate
        ? new Date(service.estimatedCompletionDate)
        : undefined,
      isDraft: service.isDraft || false,
      status: service.status,
      spareParts:
        service.spareParts?.map((part) => ({
          productId: part.id, // This might need adjustment based on your data structure
          quantity: part.quantity,
          unit: "Pcs", // Default unit
        })) || [],
    },
  });

  // Field array for spare parts
  const {
    fields: sparePartFields,
    append: appendSparePart,
    remove: removeSparePart,
  } = useFieldArray({
    control: form.control,
    name: "spareParts",
  });

  // Load products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const result = await getProducts();
        setProducts(result);
      } catch (error) {
        console.error("Error loading products:", error);
      }
    };

    loadProducts();
  }, []);

  // Handle spare part product change
  const handleSparePartChange = (index: number, productId: string) => {
    const selectedProduct = products.find((p) => p.id === productId);
    if (selectedProduct) {
      form.setValue(`spareParts.${index}.unit`, selectedProduct.unit || "Pcs");
    }
  };

  // Reset form when service data changes
  useEffect(() => {
    console.log("Service data in edit form:", service);
    console.log("Service lampiran:", service.lampiran);
    form.reset({
      serviceNumber: service.serviceNumber,
      customerName: service.customerName,
      customerPhone: service.customerPhone,
      customerEmail: service.customerEmail || "",
      deviceType: service.deviceType,
      deviceBrand: service.deviceBrand,
      deviceModel: service.deviceModel,
      deviceSerialNumber: service.deviceSerialNumber || "",
      problemDescription: service.problemDescription,
      estimatedCost: service.estimatedCost || 0,
      finalCost: service.finalCost || 0,
      estimatedCompletionDate: service.estimatedCompletionDate
        ? service.estimatedCompletionDate.split("T")[0]
        : "",
      diagnosisNotes: service.diagnosisNotes || "",
      repairNotes: service.repairNotes || "",
      customerAddress: service.customerAddress || "",
      warrantyPeriod: service.warrantyPeriod || 0,
      priorityLevel: service.priorityLevel || "MEDIUM",
      customerId: service.customerId || "",
      attachments: service.lampiran || [],
      transactionDate: service.receivedDate
        ? new Date(service.receivedDate)
        : new Date(),
      dueDate: service.estimatedCompletionDate
        ? new Date(service.estimatedCompletionDate)
        : undefined,
      isDraft: service.isDraft || false,
      status: service.status,
      spareParts:
        service.spareParts?.map((part) => ({
          productId: part.id, // This might need adjustment based on your data structure
          quantity: part.quantity,
          unit: "Pcs", // Default unit
        })) || [],
    });

    // Store original values for comparison
    const originalFormValues: ServiceFormValues = {
      serviceNumber: service.serviceNumber,
      customerName: service.customerName,
      customerPhone: service.customerPhone,
      customerEmail: service.customerEmail || "",
      deviceType: service.deviceType,
      deviceBrand: service.deviceBrand,
      deviceModel: service.deviceModel,
      deviceSerialNumber: service.deviceSerialNumber || "",
      problemDescription: service.problemDescription,
      estimatedCost: service.estimatedCost || 0,
      finalCost: service.finalCost || 0,
      estimatedCompletionDate: service.estimatedCompletionDate
        ? service.estimatedCompletionDate.split("T")[0]
        : "",
      diagnosisNotes: service.diagnosisNotes || "",
      repairNotes: service.repairNotes || "",
      customerAddress: service.customerAddress || "",
      warrantyPeriod: service.warrantyPeriod || 0,
      priorityLevel: service.priorityLevel || "MEDIUM",
      customerId: service.customerId || "",
      attachments: service.lampiran || [],
      transactionDate: service.receivedDate
        ? new Date(service.receivedDate)
        : new Date(),
      dueDate: service.estimatedCompletionDate
        ? new Date(service.estimatedCompletionDate)
        : undefined,
      isDraft: service.isDraft || false,
      status: service.status,
      spareParts:
        service.spareParts?.map((part) => ({
          productId: part.id,
          quantity: part.quantity,
          unit: "Pcs",
        })) || [],
    };
    setOriginalValues(originalFormValues);
  }, [service, form]);

  // Watch form values for unsaved changes detection
  const currentFormValues = form.watch();

  // Watch form values for summary
  const formValues = form.watch();

  // Check for unsaved changes by comparing with original values
  useEffect(() => {
    if (originalValues) {
      const hasChanges = !!(
        currentFormValues.customerName !== originalValues.customerName ||
        currentFormValues.customerPhone !== originalValues.customerPhone ||
        currentFormValues.customerEmail !== originalValues.customerEmail ||
        currentFormValues.deviceBrand !== originalValues.deviceBrand ||
        currentFormValues.deviceModel !== originalValues.deviceModel ||
        currentFormValues.problemDescription !==
          originalValues.problemDescription ||
        currentFormValues.estimatedCost !== originalValues.estimatedCost ||
        currentFormValues.finalCost !== originalValues.finalCost ||
        currentFormValues.diagnosisNotes !== originalValues.diagnosisNotes ||
        currentFormValues.repairNotes !== originalValues.repairNotes ||
        currentFormValues.status !== originalValues.status ||
        JSON.stringify(currentFormValues.spareParts) !==
          JSON.stringify(originalValues.spareParts)
      );
      setHasUnsavedChanges(hasChanges);
    }
  }, [currentFormValues, originalValues]);

  // Use the unsaved changes warning hook
  const {
    showExitDialog,
    setShowExitDialog,
    handleNavigation,
    confirmNavigation,
    cancelNavigation,
  } = useUnsavedChangesWarning(hasUnsavedChanges);

  const onSubmit = (values: ServiceFormValues) => {
    startTransition(async () => {
      try {
        // Include all the fields from the EnhancedServiceSchema
        const serviceData = {
          serviceNumber: values.serviceNumber,
          customerName: values.customerName,
          customerPhone: values.customerPhone,
          customerEmail: values.customerEmail,
          deviceType: values.deviceType,
          deviceBrand: values.deviceBrand,
          deviceModel: values.deviceModel,
          deviceSerialNumber: values.deviceSerialNumber,
          problemDescription: values.problemDescription,
          estimatedCost: values.estimatedCost,
          finalCost: values.finalCost,
          estimatedCompletionDate: values.estimatedCompletionDate,
          diagnosisNotes: values.diagnosisNotes,
          repairNotes: values.repairNotes,
          warrantyPeriod: values.warrantyPeriod,
          priorityLevel: values.priorityLevel,
          customerId: values.customerId,
          transactionDate: values.transactionDate || new Date(),
          dueDate: values.dueDate,
          isDraft: false,
          status: values.status,
          attachments: values.attachments || [],
          spareParts: values.spareParts || [],
        };

        const result = await updateService(service.id, serviceData);
        if (result.success) {
          toast.success(result.success);
          // Redirect to service detail page
          router.push(
            `/dashboard/services/management/detail/${service.serviceNumber}`
          );
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  const onSaveDraft = (values: ServiceFormValues) => {
    startTransition(async () => {
      try {
        // Include all the fields from the EnhancedServiceSchema
        const serviceData = {
          serviceNumber: values.serviceNumber,
          customerName: values.customerName,
          customerPhone: values.customerPhone,
          customerEmail: values.customerEmail,
          deviceType: values.deviceType,
          deviceBrand: values.deviceBrand,
          deviceModel: values.deviceModel,
          deviceSerialNumber: values.deviceSerialNumber,
          problemDescription: values.problemDescription,
          estimatedCost: values.estimatedCost,
          finalCost: values.finalCost,
          estimatedCompletionDate: values.estimatedCompletionDate,
          diagnosisNotes: values.diagnosisNotes,
          repairNotes: values.repairNotes,
          warrantyPeriod: values.warrantyPeriod,
          priorityLevel: values.priorityLevel,
          customerId: values.customerId,
          transactionDate: values.transactionDate || new Date(),
          dueDate: values.dueDate,
          isDraft: true,
          status: values.status,
          attachments: values.attachments || [],
          spareParts: values.spareParts || [],
        };

        const result = await updateService(service.id, serviceData);
        if (result.success) {
          toast.success("Draft servis berhasil disimpan!");
          // Redirect to services page with draft tab
          router.push("/dashboard/services/management?tab=drafts");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Edit Servis</h1>
            <p className="text-muted-foreground">
              Edit informasi servis {service.serviceNumber}
            </p>
          </div>
          <Button variant="outline" asChild className="gap-2 cursor-pointer">
            <Link
              href={`/dashboard/services/management/detail/${service.serviceNumber}`}
              className="cursor-pointer"
            >
              <ArrowLeft className="h-4 w-4" />
              Kembali
            </Link>
          </Button>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Form Content */}
              <div className="lg:col-span-2">
                <CombinedServiceForm
                  control={form.control}
                  isPending={isPending}
                  setValue={form.setValue}
                  trigger={form.trigger}
                  title="Edit Formulir Servis"
                  description="Perbarui informasi servis yang diperlukan"
                  isEditMode={true}
                  products={products}
                  spareParts={form.watch("spareParts")}
                  sparePartFields={sparePartFields}
                  appendSparePart={appendSparePart}
                  removeSparePart={removeSparePart}
                  handleSparePartChange={handleSparePartChange}
                />
              </div>

              {/* Summary Sidebar */}
              <div className="lg:col-span-1">
                <EnhancedServiceFormSummary
                  formValues={formValues}
                  isPending={isPending}
                />

                <div className="mt-6 space-y-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={form.handleSubmit(onSaveDraft)}
                    className="w-full gap-2 cursor-pointer"
                    disabled={isPending}
                  >
                    <Save className="h-4 w-4" />
                    {isPending ? "Menyimpan..." : "Simpan ke Draft"}
                  </Button>
                  <Button
                    type="submit"
                    className="w-full gap-2 cursor-pointer"
                    disabled={isPending}
                  >
                    <Check className="h-4 w-4" />
                    {isPending ? "Menyimpan..." : "Simpan Perubahan"}
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </Form>

        {/* Unsaved Changes Dialog */}
        <AlertDialog open={showExitDialog} onOpenChange={setShowExitDialog}>
          <AlertDialogContent className="w-[95%] max-w-md md:max-w-xl lg:max-w-2xl">
            <AlertDialogHeader>
              <AlertDialogTitle>Perubahan Belum Tersimpan</AlertDialogTitle>
              <AlertDialogDescription>
                Anda memiliki perubahan yang belum tersimpan. Jika Anda
                meninggalkan halaman ini, perubahan Anda akan hilang.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2 md:justify-end">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2 w-full">
                <AlertDialogCancel
                  onClick={cancelNavigation}
                  className="cursor-pointer w-full"
                >
                  Kembali ke Form
                </AlertDialogCancel>
                <AlertDialogAction
                  onClick={confirmNavigation}
                  className="cursor-pointer bg-red-500 hover:bg-red-600 w-full"
                >
                  Buang Perubahan
                </AlertDialogAction>
                <Button
                  type="button"
                  variant="default"
                  onClick={form.handleSubmit((values) => {
                    onSaveDraft(values);
                    setShowExitDialog(false);
                  })}
                  className="cursor-pointer w-full"
                >
                  Simpan ke Draft
                </Button>
              </div>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </DashboardLayout>
  );
};

export default ServiceEditPage;
