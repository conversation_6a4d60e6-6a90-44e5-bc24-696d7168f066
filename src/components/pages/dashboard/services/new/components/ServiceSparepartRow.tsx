"use client";

import React, { useState, useRef, useEffect } from "react";
// Removed Next.js specific imports as they are not available in this environment.
// Image and Link components will be replaced with standard HTML elements or alternative UI.
// import Image from "next/image";
// import Link from "next/link";
import { createPortal } from "react-dom";
import {
  Control,
  UseFieldArrayRemove,
  FieldValues,
  useFormContext,
} from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { TrashIcon } from "@heroicons/react/24/outline";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChevronDown, ChevronUp, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { ServiceFormValues, Product } from "../types";

interface ServiceSparepartRowProps {
  control: Control<ServiceFormValues>;
  index: number;
  field: FieldValues;
  products: Product[];
  spareParts: ServiceFormValues["spareParts"];
  remove: UseFieldArrayRemove;
  handleProductChange: (index: number, productId: string) => void;
  isPending: boolean;
  canRemove: boolean;
}

const ServiceSparepartRow: React.FC<ServiceSparepartRowProps> = ({
  control,
  index,
  field: _,
  products,
  spareParts,
  remove,
  handleProductChange,
  isPending,
  canRemove,
}) => {
  const form = useFormContext<ServiceFormValues>();
  const sparePart = spareParts[index];

  return (
    <tr className="border-b border-gray-200 dark:border-gray-700 h-20">
      {/* Product Selection */}
      <td className="py-4 pl-0 pr-2" style={{ minWidth: "300px" }}>
        <FormField
          control={control}
          name={`spareParts.${index}.productId`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <ProductDropdown
                    value={formField.value}
                    onChange={(value) => {
                      formField.onChange(value);
                      handleProductChange(index, value);
                    }}
                    products={products}
                    disabled={isPending}
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Quantity */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`spareParts.${index}.quantity`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    step="1"
                    placeholder="1"
                    {...formField}
                    value={formField.value || ""}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 1;
                      formField.onChange(value);
                    }}
                    disabled={isPending}
                    className="text-center"
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Unit */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`spareParts.${index}.unit`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <Select
                    value={formField.value}
                    onValueChange={formField.onChange}
                    disabled={isPending}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Unit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Pcs">Pcs</SelectItem>
                      <SelectItem value="Set">Set</SelectItem>
                      <SelectItem value="Unit">Unit</SelectItem>
                      <SelectItem value="Buah">Buah</SelectItem>
                      <SelectItem value="Meter">Meter</SelectItem>
                      <SelectItem value="Kg">Kg</SelectItem>
                      <SelectItem value="Liter">Liter</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Remove Button */}
      <td className="py-4 px-0 text-right" style={{ minWidth: "70px" }}>
        {canRemove && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => remove(index)}
            disabled={isPending}
            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 cursor-pointer"
          >
            <TrashIcon className="h-5 w-5" />
          </Button>
        )}
      </td>
    </tr>
  );
};

// Custom Product Dropdown Component with Images and Search
interface ProductDropdownProps {
  value: string;
  onChange: (value: string) => void;
  products: Product[];
  disabled?: boolean;
}

const ProductDropdown: React.FC<ProductDropdownProps> = ({
  value,
  onChange,
  products,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const selectedProduct = products.find((product) => product.id === value);

  // Handle mounting for client-side rendering
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Close dropdown when pressing escape
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsOpen(false);
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Update position when dropdown is opened, scrolled, or window is resized
  useEffect(() => {
    const updatePosition = () => {
      if (isOpen && dropdownRef.current) {
        const rect = dropdownRef.current.getBoundingClientRect();
        const dropdownWidth = 300; // Width of dropdown in pixels

        // Check if dropdown would go off the right edge of the screen
        const rightEdge = rect.left + dropdownWidth;
        const windowWidth = window.innerWidth;

        // If it would go off-screen, adjust the left position
        const adjustedLeft =
          rightEdge > windowWidth
            ? Math.max(0, windowWidth - dropdownWidth)
            : rect.left;

        // Calculate position relative to the viewport (for absolute positioning)
        setPosition({
          top: rect.bottom + window.scrollY + 5,
          left: adjustedLeft,
        });
      }
    };

    updatePosition();

    // Add event listeners for resize and scroll
    window.addEventListener("resize", updatePosition);
    window.addEventListener("scroll", updatePosition, true);

    return () => {
      window.removeEventListener("resize", updatePosition);
      window.removeEventListener("scroll", updatePosition, true);
    };
  }, [isOpen]);

  // Search products from API
  const searchProducts = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(
        `/api/products/search?q=${encodeURIComponent(query)}&limit=10`
      );
      if (!response.ok) throw new Error("Failed to search products");

      const data = await response.json();
      setSearchResults(data.products || []);
    } catch (error) {
      console.error("Error searching products:", error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      searchProducts(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Get products to display - either search results or 3 newest products
  const displayProducts = searchTerm
    ? searchResults
    : [...products]
        .sort((a, b) => {
          // Sort by createdAt if available, otherwise use id as fallback
          if (a.createdAt && b.createdAt) {
            const dateA =
              a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt);
            const dateB =
              b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt);
            return dateB.getTime() - dateA.getTime();
          }
          // Fallback to id comparison if createdAt is not available
          return a.id > b.id ? -1 : 1; // Newer IDs are typically longer/greater
        })
        .slice(0, 3);

  return (
    <div className="relative" ref={dropdownRef}>
      <div
        className={`flex items-center justify-between w-full rounded-md border ${
          disabled
            ? "bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700"
            : "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
        } py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm text-gray-900 dark:text-gray-100 cursor-pointer`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-3 overflow-hidden">
          {selectedProduct?.image ? (
            // Using a standard img tag and div since next/image is not available
            <div className="h-7 w-7 rounded-sm overflow-hidden flex-shrink-0">
              <img
                src={selectedProduct.image}
                alt={selectedProduct.name}
                className="object-cover w-full h-full"
              />
            </div>
          ) : selectedProduct ? (
            <div className="h-7 w-7 bg-gray-200 dark:bg-gray-600 rounded-sm flex items-center justify-center flex-shrink-0">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                No img
              </span>
            </div>
          ) : (
            <div className="h-7 w-7 bg-gray-100 dark:bg-gray-700 rounded-sm flex items-center justify-center flex-shrink-0">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                Img
              </span>
            </div>
          )}
          <span className="truncate font-medium">
            {selectedProduct
              ? selectedProduct.name
              : "Pilih produk/sparepart..."}
          </span>
        </div>
        {!disabled &&
          (isOpen ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          ))}
      </div>

      {mounted &&
        isOpen &&
        !disabled &&
        createPortal(
          <>
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            <div
              className="absolute z-50 w-[300px] rounded-md bg-white dark:bg-gray-800 shadow-xl max-h-[300px] overflow-auto border border-gray-200 dark:border-gray-700"
              style={{
                top: position.top,
                left: position.left,
                maxWidth: "calc(100vw - 20px)",
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="py-1">
                {/* Search input */}
                <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
                  <div className="relative">
                    <input
                      ref={searchInputRef}
                      type="text"
                      placeholder="Cari produk..."
                      className="w-full px-3 py-1.5 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm text-gray-900 dark:text-gray-100 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                </div>

                {/* Default option */}
                <div
                  className="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-700"
                  onClick={() => {
                    onChange("");
                    setIsOpen(false);
                  }}
                >
                  Pilih produk/sparepart...
                </div>

                {/* Loading indicator */}
                {isSearching && (
                  <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                    Mencari...
                  </div>
                )}

                {/* No results message */}
                {!isSearching && searchTerm && displayProducts.length === 0 && (
                  <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                    Tidak ada produk ditemukan
                  </div>
                )}

                {/* Product list */}
                {displayProducts.map((product) => (
                  <div
                    key={product.id}
                    className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                    onClick={() => {
                      onChange(product.id);
                      setIsOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-3">
                      {product.image ? (
                        // Using a standard img tag and div
                        <div className="h-8 w-8 rounded-sm overflow-hidden flex-shrink-0">
                          <img
                            src={product.image}
                            alt={product.name}
                            className="object-cover w-full h-full"
                          />
                        </div>
                      ) : (
                        <div className="h-8 w-8 bg-gray-200 dark:bg-gray-600 rounded-sm flex items-center justify-center flex-shrink-0">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            No img
                          </span>
                        </div>
                      )}
                      <span className="truncate">{product.name}</span>
                    </div>
                  </div>
                ))}

                {/* Add Product Link */}
                <a
                  href="/dashboard/products/new" // Changed to standard anchor tag
                  className="mt-2 px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-t border-gray-200 dark:border-gray-700 flex items-center gap-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsOpen(false);
                  }}
                >
                  <Plus className="h-4 w-4" />
                  <span>Tambah Produk</span>
                </a>
              </div>
            </div>
          </>,
          document.body
        )}
    </div>
  );
};

export default ServiceSparepartRow;
