"use client";

import React, { useTransition, useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { addService } from "@/actions/entities/services";
import { getProducts } from "@/lib/get-products";
import { useTransactionLimits } from "@/hooks/useSubscriptionLimits"; // Added import
import { Card, CardContent } from "@/components/ui/card"; // Added import
import { AlertCircle } from "lucide-react"; // Added import

import DashboardLayout from "@/components/layout/dashboardlayout";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";

import { EnhancedServiceSchema } from "./types";
import { ServiceFormValues, Product } from "./types";
import CombinedServiceForm from "./components/CombinedServiceForm";
import { ArrowLeft, Check, Save } from "lucide-react";
import { DeviceType } from "../types";
import { useUnsavedChangesWarning } from "@/hooks/useUnsavedChangesWarning";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const AddServicePage: React.FC = () => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [products, setProducts] = useState<Product[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Added transaction limits hook
  const {
    canCreateTransaction,
    transactionMessage,
    currentTransactionUsage,
    transactionLimit,
    isLoading: limitsLoading,
  } = useTransactionLimits();

  // Initialize the form with enhanced schema
  const form = useForm<ServiceFormValues>({
    resolver: zodResolver(EnhancedServiceSchema),
    defaultValues: {
      serviceNumber: "", // Will be auto-generated
      customerName: "",
      customerPhone: "",
      customerEmail: "",
      deviceType: DeviceType.OTHER,
      deviceBrand: "",
      deviceModel: "",
      deviceSerialNumber: "",
      problemDescription: "",
      estimatedCost: 0,
      finalCost: 0,
      estimatedCompletionDate: "",
      diagnosisNotes: "",
      repairNotes: "",
      customerAddress: "",
      warrantyPeriod: 0,
      priorityLevel: "MEDIUM",
      customerId: "",
      attachments: [],
      transactionDate: new Date(),
      dueDate: undefined,
      spareParts: [],
    },
  });

  // Field array for spare parts
  const {
    fields: sparePartFields,
    append: appendSparePart,
    remove: removeSparePart,
  } = useFieldArray({
    control: form.control,
    name: "spareParts",
  });

  // Watch form values for unsaved changes detection
  const formValues = form.watch();

  // Check for unsaved changes
  useEffect(() => {
    const hasChanges = !!(
      formValues.customerName ||
      formValues.customerPhone ||
      formValues.deviceBrand ||
      formValues.deviceModel ||
      formValues.problemDescription ||
      (formValues.estimatedCost ?? 0) > 0 ||
      (formValues.finalCost ?? 0) > 0 ||
      formValues.diagnosisNotes ||
      formValues.repairNotes ||
      (formValues.spareParts && formValues.spareParts.length > 0)
    );
    setHasUnsavedChanges(hasChanges);
  }, [formValues]);

  // Use the unsaved changes warning hook
  const {
    showExitDialog,
    setShowExitDialog,
    handleNavigation,
    confirmNavigation,
    cancelNavigation,
  } = useUnsavedChangesWarning(hasUnsavedChanges);

  // Load products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const result = await getProducts();
        setProducts(result);
      } catch (error) {
        console.error("Error loading products:", error);
      }
    };

    loadProducts();
  }, []);

  // Handle spare part product change
  const handleSparePartChange = (index: number, productId: string) => {
    const selectedProduct = products.find((p) => p.id === productId);
    if (selectedProduct) {
      form.setValue(`spareParts.${index}.unit`, selectedProduct.unit || "Pcs");
    }
  };

  const onSubmit = (values: ServiceFormValues) => {
    // Check subscription limits before submission
    if (!canCreateTransaction) {
      toast.error(
        transactionMessage || "Batas transaksi tercapai untuk paket Anda."
      );
      return;
    }

    startTransition(async () => {
      try {
        // Show loading toast
        const toastId = toast.loading("Menyimpan data servis...");

        // Send the data to the server with isDraft = false
        const result = await addService({ ...values, isDraft: false });

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          // Show success message
          toast.success(result.success);

          // Reset form and state
          form.reset({
            serviceNumber: "", // Will be auto-generated
            customerName: "",
            customerPhone: "",
            customerEmail: "",
            deviceType: DeviceType.OTHER,
            deviceBrand: "",
            deviceModel: "",
            deviceSerialNumber: "",
            problemDescription: "",
            estimatedCost: 0,
            finalCost: 0,
            estimatedCompletionDate: "",
            diagnosisNotes: "",
            repairNotes: "",
            customerAddress: "",
            warrantyPeriod: 0,
            priorityLevel: "MEDIUM",
            customerId: "",
            attachments: [],
            transactionDate: new Date(),
            dueDate: undefined,
            spareParts: [],
          });

          // Redirect to services page
          router.push("/dashboard/services/management");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  const onSaveDraft = (values: ServiceFormValues) => {
    // Check subscription limits before saving as draft
    if (!canCreateTransaction) {
      toast.error(
        transactionMessage || "Batas transaksi tercapai untuk paket Anda."
      );
      return;
    }

    startTransition(async () => {
      try {
        // Show loading toast
        const toastId = toast.loading("Menyimpan draft servis...");

        // Send the data to the server with isDraft = true
        const result = await addService({ ...values, isDraft: true });

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          // Show success message
          toast.success("Draft servis berhasil disimpan!");

          // Reset form and state
          form.reset({
            serviceNumber: "", // Will be auto-generated
            customerName: "",
            customerPhone: "",
            customerEmail: "",
            deviceType: DeviceType.OTHER,
            deviceBrand: "",
            deviceModel: "",
            deviceSerialNumber: "",
            problemDescription: "",
            estimatedCost: 0,
            finalCost: 0,
            estimatedCompletionDate: "",
            diagnosisNotes: "",
            repairNotes: "",
            customerAddress: "",
            warrantyPeriod: 0,
            priorityLevel: "MEDIUM",
            customerId: "",
            attachments: [],
            transactionDate: new Date(),
            dueDate: undefined,
            spareParts: [],
          });

          // Redirect to services page with draft tab
          router.push("/dashboard/services/management?tab=drafts");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 pt-6 h-full">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">Tambah Servis Baru</h1>
            <p className="text-muted-foreground">
              Tambahkan servis baru ke sistem manajemen servis Anda
            </p>
          </div>
          <Button variant="outline" asChild className="gap-2 cursor-pointer">
            <Link
              href="/dashboard/services/management"
              className="cursor-pointer"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden md:flex">Kembali</span>
            </Link>
          </Button>
        </div>

        {/* Subscription Limit Warning */}
        {transactionLimit && currentTransactionUsage !== undefined && (
          <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950 mb-6">
            <CardContent className="pt-0">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                    Batas Transaksi Bulanan
                  </p>
                  <p className="text-sm text-orange-700 dark:text-orange-300">
                    Anda telah menggunakan {currentTransactionUsage} dari{" "}
                    {transactionLimit} transaksi yang tersedia bulan ini.
                    {transactionLimit - currentTransactionUsage <= 5 && (
                      <span className="font-medium">
                        {" "}
                        Sisa {transactionLimit - currentTransactionUsage}{" "}
                        transaksi lagi.
                      </span>
                    )}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid gap-6">
              {/* Main Form Content */}
              <div className="lg:col-span-2">
                <CombinedServiceForm
                  control={form.control}
                  isPending={isPending}
                  setValue={form.setValue}
                  trigger={form.trigger}
                  products={products}
                  spareParts={form.watch("spareParts")}
                  sparePartFields={sparePartFields}
                  appendSparePart={appendSparePart}
                  removeSparePart={removeSparePart}
                  handleSparePartChange={handleSparePartChange}
                />
              </div>
            </div>

            {/* Form Actions */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                asChild
                disabled={isPending}
              >
                <Link
                  href="/dashboard/services/management"
                  className="cursor-pointer"
                >
                  Batal
                </Link>
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={form.handleSubmit(onSaveDraft)}
                disabled={isPending}
                className="gap-2 cursor-pointer"
              >
                {isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                    <span>Menyimpan...</span>
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    <span>
                      <span className="hidden md:inline">Simpan ke </span>Draft
                    </span>
                  </>
                )}
              </Button>
              <Button
                type="submit"
                disabled={isPending}
                className="gap-2 cursor-pointer"
              >
                {isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Menyimpan...</span>
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4" />
                    <span>Simpan Servis</span>
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>

        {/* Unsaved Changes Dialog */}
        <AlertDialog open={showExitDialog} onOpenChange={setShowExitDialog}>
          <AlertDialogContent className="w-[95%] max-w-md md:max-w-xl lg:max-w-2xl">
            <AlertDialogHeader>
              <AlertDialogTitle>Perubahan Belum Tersimpan</AlertDialogTitle>
              <AlertDialogDescription>
                Anda memiliki perubahan yang belum tersimpan. Jika Anda
                meninggalkan halaman ini, perubahan Anda akan hilang.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2 md:justify-end">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2 w-full">
                <AlertDialogCancel
                  onClick={cancelNavigation}
                  className="cursor-pointer w-full"
                >
                  Kembali ke Form
                </AlertDialogCancel>
                <AlertDialogAction
                  onClick={confirmNavigation}
                  className="cursor-pointer bg-red-500 hover:bg-red-600 w-full"
                >
                  Buang Perubahan
                </AlertDialogAction>
                <Button
                  type="button"
                  variant="default"
                  onClick={form.handleSubmit((values) => {
                    onSaveDraft(values);
                    setShowExitDialog(false);
                  })}
                  className="cursor-pointer w-full"
                >
                  Simpan ke Draft
                </Button>
              </div>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </DashboardLayout>
  );
};

export default AddServicePage;
