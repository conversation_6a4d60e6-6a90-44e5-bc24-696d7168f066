"use client";

import React, { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Upload,
  CheckCircle,
  Info,
  Download,
  AlertCircle,
  FileX,
  XCircle,
  Clock,
  Package,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import { createPurchaseImportTemplate } from "@/utils/imports/purchasesImportTemplate";
import {
  importPurchases,
  importPurchasesWithConflictResolution,
} from "@/actions/import/purchasesImport";
import { Separator } from "@/components/ui/separator";

interface ImportSummary {
  purchasesCreated: number;
  suppliersCreated: number;
  warehousesCreated: number;
  errors: string[];
  skippedRows?: number;
  conflictedInvoices?: string[];
}

interface ConflictDetails {
  duplicateInvoices: string[];
  affectedRows: number[];
  validRowsCount: number;
  totalRowsCount: number;
}

interface ImportResult {
  success?: string;
  error?: string;
  summary: ImportSummary;
  hasConflicts?: boolean;
  conflictDetails?: ConflictDetails;
}

interface PurchaseImportButtonProps {
  onRefresh?: () => void;
}

export const PurchaseImportButton: React.FC<PurchaseImportButtonProps> = ({
  onRefresh,
}) => {
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showConflictDialog, setShowConflictDialog] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(
    null
  );
  const [conflictDetails, setConflictDetails] =
    useState<ConflictDetails | null>(null);
  const [pendingFileBuffer, setPendingFileBuffer] =
    useState<ArrayBuffer | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const downloadTemplate = () => {
    try {
      const workbook = createPurchaseImportTemplate();
      const fileName = `template-import-pembelian-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template");
    }
  };

  const resetImportState = () => {
    setIsImporting(false);
    setImportProgress(0);
    setImportSummary(null);
    setConflictDetails(null);
    setPendingFileBuffer(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleImportWithConflicts = async () => {
    if (!pendingFileBuffer) return;

    setShowConflictDialog(false);
    setIsImporting(true);
    setImportProgress(0);

    try {
      setImportProgress(20);
      const result =
        await importPurchasesWithConflictResolution(pendingFileBuffer);
      setImportProgress(80);

      if (result.success) {
        setImportProgress(100);
        setImportSummary(result.summary);

        // Create detailed success message
        let successMessage = result.success;
        if (
          result.summary.conflictedInvoices &&
          result.summary.conflictedInvoices.length > 0
        ) {
          successMessage += ` ${result.summary.conflictedInvoices.length} invoice yang bertentangan diabaikan.`;
        }

        toast.success(successMessage + " Lihat detail di bawah.");

        if (onRefresh) {
          setTimeout(() => {
            onRefresh();
          }, 1500);
        }
      } else if (result.error) {
        setImportSummary(result.summary);
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Import with conflict resolution error:", error);
      toast.error(
        "Gagal mengimpor file dengan penyelesaian konflik atau tidak valid"
      );
      setImportSummary({
        purchasesCreated: 0,
        suppliersCreated: 0,
        warehousesCreated: 0,
        errors: ["Gagal memproses file"],
        skippedRows: 0,
        conflictedInvoices: [],
      });
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      setPendingFileBuffer(null);
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.match(/\.(xlsx|xls)$/)) {
      toast.error(
        "Format file tidak didukung. Gunakan file Excel (.xlsx atau .xls)"
      );
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file terlalu besar. Maksimal 10MB");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportSummary(null);

    try {
      setImportProgress(20);
      const arrayBuffer = await file.arrayBuffer();
      setImportProgress(40);
      const result: ImportResult = await importPurchases(arrayBuffer);
      setImportProgress(80);

      // Check if there are conflicts
      if (result.hasConflicts && result.conflictDetails) {
        setImportProgress(100);
        setConflictDetails(result.conflictDetails);
        setPendingFileBuffer(arrayBuffer);
        setShowConflictDialog(true);
        setIsImporting(false);
        return;
      }

      if (result.success) {
        setImportProgress(100);
        setImportSummary(result.summary);
        toast.success(result.success + " Lihat detail di bawah.");

        if (onRefresh) {
          setTimeout(() => {
            onRefresh();
          }, 1500);
        }
      } else if (result.error) {
        setImportSummary(result.summary);

        // Enhanced error message handling
        let errorMessage = result.error;
        if (result.summary?.errors && result.summary.errors.length > 0) {
          const firstError = result.summary.errors[0];
          if (
            firstError.includes("Produk") &&
            firstError.includes("tidak ditemukan")
          ) {
            errorMessage =
              "Beberapa produk dalam file tidak ditemukan di sistem. Pastikan nama produk sama persis (case-sensitive).";
          } else if (firstError.includes("case-sensitive")) {
            errorMessage =
              "Nama produk harus sama persis dengan yang ada di sistem (case-sensitive).";
          }
        }
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Import error:", error);
      let errorMessage = "Gagal mengimpor file";
      if (error instanceof Error) {
        if (error.message.includes("timeout")) {
          errorMessage =
            "Import timeout. File terlalu besar atau koneksi lambat. Coba dengan file yang lebih kecil.";
        } else if (error.message.includes("authentication")) {
          errorMessage = "Sesi Anda telah berakhir. Silakan login kembali.";
        }
      }
      toast.error(errorMessage);
      setImportSummary({
        purchasesCreated: 0,
        suppliersCreated: 0,
        warehousesCreated: 0,
        errors: [errorMessage],
        skippedRows: 0,
        conflictedInvoices: [],
      });
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Main Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col rounded-md">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Pembelian
            </DialogTitle>
            <DialogDescription className="flex text-left">
              Import data pembelian dari file Excel dengan mudah dan aman
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Cara Import Data Pembelian:
                  </h4>
                  <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
                    <li>
                      Download template Excel dengan klik tombol "Download
                      Template"
                    </li>
                    <li>Isi data pembelian sesuai format yang tersedia</li>
                    <li>
                      <strong>Pastikan nama produk sama persis</strong> dengan
                      yang ada di sistem
                    </li>
                    <li>Upload file Excel yang sudah diisi</li>
                    <li>Tunggu proses import selesai</li>
                  </ol>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">1. Download Template Excel</h4>
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="w-full cursor-pointer"
              >
                <Download className="mr-2 h-4 w-4" />
                Download Template Pembelian
              </Button>
            </div>

            <Separator />

            <div className="space-y-2">
              <h4 className="font-medium">2. Upload File Excel</h4>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
                <p className="text-sm text-slate-600 mb-2">
                  Klik untuk memilih file atau drag & drop
                </p>
                <p className="text-xs text-slate-500">
                  Format: .xlsx, .xls (Maksimal 10MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4 cursor-pointer"
                  disabled={isImporting}
                >
                  {isImporting ? "Memproses..." : "Pilih File Excel"}
                </Button>
              </div>
            </div>

            {isImporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress Import</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} className="w-full" />
              </div>
            )}

            {importSummary && (
              <div className="space-y-4">
                <h4 className="font-medium">Hasil Import:</h4>

                {/* Success Statistics */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">
                        Pembelian Dibuat
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {importSummary.purchasesCreated}
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium">Supplier Baru</span>
                    </div>
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {importSummary.suppliersCreated}
                    </div>
                  </div>
                </div>

                {/* Additional Statistics */}
                {(importSummary.skippedRows! > 0 ||
                  importSummary.conflictedInvoices!.length > 0) && (
                  <div className="grid grid-cols-1 gap-4">
                    {importSummary.skippedRows! > 0 && (
                      <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-orange-500" />
                          <span className="text-sm font-medium">
                            Data Diabaikan
                          </span>
                        </div>
                        <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
                          {importSummary.skippedRows} baris
                        </div>
                        <div className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                          Data tidak valid atau konflik
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Conflicted Invoices */}
                {importSummary.conflictedInvoices &&
                  importSummary.conflictedInvoices.length > 0 && (
                    <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <FileX className="h-4 w-4 text-amber-600" />
                        <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                          Invoice yang Diabaikan
                        </span>
                      </div>
                      <div className="text-xs text-amber-700 dark:text-amber-300 space-y-1">
                        {importSummary.conflictedInvoices
                          .slice(0, 3)
                          .map((invoice, index) => (
                            <div key={index}>• {invoice}</div>
                          ))}
                        {importSummary.conflictedInvoices.length > 3 && (
                          <div>
                            ... dan{" "}
                            {importSummary.conflictedInvoices.length - 3}{" "}
                            lainnya
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                {/* Errors Section */}
                {importSummary.errors.length > 0 && (
                  <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                    <h5 className="font-medium text-red-800 dark:text-red-200 mb-2 flex items-center gap-2">
                      <XCircle className="h-4 w-4" />
                      Error ({importSummary.errors.length}):
                    </h5>
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {importSummary.errors.slice(0, 5).map((error, index) => (
                        <div
                          key={index}
                          className="text-sm text-red-700 dark:text-red-300"
                        >
                          • {error}
                        </div>
                      ))}
                      {importSummary.errors.length > 5 && (
                        <div className="text-sm text-red-700 dark:text-red-300">
                          ... dan {importSummary.errors.length - 5} error
                          lainnya
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Summary Text */}
                <div className="bg-slate-50 dark:bg-slate-900/20 p-3 rounded-lg">
                  <h5 className="font-medium text-slate-800 dark:text-slate-200 mb-2">
                    Ringkasan Import:
                  </h5>
                  <div className="text-sm text-slate-700 dark:text-slate-300 space-y-1">
                    <p>
                      ✅ <strong>{importSummary.purchasesCreated}</strong>{" "}
                      pembelian berhasil diimpor
                    </p>
                    <p>
                      👥 <strong>{importSummary.suppliersCreated}</strong>{" "}
                      supplier baru dibuat
                    </p>
                    {importSummary.skippedRows! > 0 && (
                      <p>
                        ⚠️ <strong>{importSummary.skippedRows}</strong> baris
                        diabaikan karena error atau konflik
                      </p>
                    )}
                    {importSummary.errors.length > 0 && (
                      <p>
                        ❌ <strong>{importSummary.errors.length}</strong> error
                        ditemukan dalam proses import
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Conflict Resolution Dialog */}
      <Dialog open={showConflictDialog} onOpenChange={setShowConflictDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Konflik Data Import
            </DialogTitle>
            <DialogDescription>
              Ditemukan invoice yang sudah ada dalam sistem
            </DialogDescription>
          </DialogHeader>

          {conflictDetails && (
            <div className="space-y-4">
              {/* Conflict Summary */}
              <div className="rounded-lg bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 p-4">
                <div className="flex items-start gap-3">
                  <FileX className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                      Invoice yang Bertentangan
                    </h4>
                    <div className="mt-2 text-sm text-amber-700 dark:text-amber-300">
                      <p>
                        Ditemukan{" "}
                        <strong>
                          {conflictDetails.duplicateInvoices.length}
                        </strong>{" "}
                        invoice yang sudah ada dalam sistem:
                      </p>
                      <div className="mt-2 max-h-20 overflow-y-auto">
                        <ul className="list-disc list-inside space-y-1">
                          {conflictDetails.duplicateInvoices
                            .slice(0, 3)
                            .map((invoice, index) => (
                              <li key={index} className="text-xs">
                                {invoice}
                              </li>
                            ))}
                          {conflictDetails.duplicateInvoices.length > 3 && (
                            <li className="text-xs">
                              ... dan{" "}
                              {conflictDetails.duplicateInvoices.length - 3}{" "}
                              lainnya
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Import Statistics */}
              <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Info className="h-5 w-5 text-blue-600" />
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Statistik Import
                  </h4>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-blue-600 dark:text-blue-400 font-semibold">
                      {conflictDetails.validRowsCount}
                    </div>
                    <div className="text-blue-700 dark:text-blue-300">
                      Data Valid
                    </div>
                  </div>
                  <div>
                    <div className="text-amber-600 dark:text-amber-400 font-semibold">
                      {conflictDetails.affectedRows.length}
                    </div>
                    <div className="text-amber-700 dark:text-amber-300">
                      Tidak Valid
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-sm text-slate-600 dark:text-slate-400">
                <p>
                  Apakah Anda ingin melanjutkan import dengan mengabaikan data
                  yang bertentangan?
                  <strong className="text-slate-800 dark:text-slate-200">
                    {" "}
                    {conflictDetails.validRowsCount}{" "}
                  </strong>
                  data valid akan diimpor.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-2">
                <Button
                  onClick={handleImportWithConflicts}
                  className="flex-1"
                  disabled={isImporting}
                >
                  {isImporting ? "Mengimpor..." : "Ya, Lanjutkan Import"}
                </Button>
                <Button
                  onClick={() => {
                    setShowConflictDialog(false);
                    resetImportState();
                  }}
                  variant="outline"
                  className="flex-1"
                  disabled={isImporting}
                >
                  Batal
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
