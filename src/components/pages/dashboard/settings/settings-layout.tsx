"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Role } from "@prisma/client";
import { PermissionCheck } from "@/components/auth/permission-check";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  UserCircle,
  Users,
  Palette,
  Bell,
  CreditCard,
  Receipt,
  ShieldCheck,
  HelpCircle,
  ChevronRight,
  ExternalLink,
  Settings,
  LayoutGrid,
} from "lucide-react";

interface SettingsLayoutProps {
  children: React.ReactNode;
}

interface SettingsNavItem {
  title: string;
  href: string;
  icon: React.ElementType;
  roles?: Role[];
  description: string;
  category: "account" | "preferences" | "billing" | "security" | "support";
}

const settingsNavItems: SettingsNavItem[] = [
  {
    title: "Profile",
    href: "/settings/profile",
    icon: UserCircle,
    description: "Kelola informasi pribadi dan preferensi akun",
    category: "account",
  },
  {
    title: "Bisnis",
    href: "/settings/business",
    icon: LayoutGrid,
    description: "Pengaturan informasi bisnis dan perusahaan",
    category: "account",
  },
  {
    title: "Karyawan",
    href: "/settings/employees",
    icon: Users,
    description: "Kelola tim dan hak akses karyawan",
    category: "account",
    roles: [Role.OWNER],
  },
  {
    title: "Admin Tools",
    href: "/settings/admin",
    icon: Settings,
    description: "Tools administratif dan maintenance",
    category: "account",
    roles: [Role.OWNER],
  },
  {
    title: "Tampilan",
    href: "/settings/appearance",
    icon: Palette,
    description: "Sesuaikan tema dan tampilan aplikasi",
    category: "preferences",
  },
  {
    title: "Notifikasi",
    href: "/settings/notifications",
    icon: Bell,
    description: "Atur preferensi notifikasi dan pemberitahuan",
    category: "preferences",
  },
  {
    title: "Redirect Settings",
    href: "/settings/redirections",
    icon: ExternalLink,
    description: "Konfigurasi pengalihan URL dan domain",
    category: "preferences",
  },
  {
    title: "Billing",
    href: "/settings/billing",
    icon: CreditCard,
    description: "Kelola metode pembayaran dan riwayat tagihan",
    category: "billing",
  },
  {
    title: "Plan & Tagihan",
    href: "/settings/plans",
    icon: Receipt,
    description: "Lihat dan upgrade paket langganan Anda",
    category: "billing",
  },
  {
    title: "Keamanan",
    href: "/settings/security",
    icon: ShieldCheck,
    description: "Kelola keamanan akun dan autentikasi",
    category: "security",
  },
  {
    title: "Support",
    href: "/settings/support",
    icon: HelpCircle,
    description: "Bantuan, dokumentasi, dan dukungan teknis",
    category: "support",
  },
];

const categoryLabels = {
  account: "Akun & Profil",
  preferences: "Preferensi",
  billing: "Billing & Langganan",
  security: "Keamanan",
  support: "Bantuan",
};

const categoryColors = {
  account: "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300",
  preferences:
    "bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300",
  billing:
    "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300",
  security: "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300",
  support:
    "bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300",
};

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const pathname = usePathname();
  const [activeItem, setActiveItem] = useState("");

  useEffect(() => {
    const matchingItem = settingsNavItems.find((item) =>
      pathname.includes(item.href)
    );

    if (matchingItem) {
      setActiveItem(matchingItem.href);
    } else if (pathname === "/dashboard/settings") {
      setActiveItem("/dashboard/settings/profile");
    }
  }, [pathname]);

  const groupedItems = settingsNavItems.reduce(
    (acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = [];
      }
      acc[item.category].push(item);
      return acc;
    },
    {} as Record<string, SettingsNavItem[]>
  );

  return (
    <div className="bg-gray-50 dark:bg-gray-900">
      <div className="mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-15 gap-6">
          {/* Sidebar Navigation - Fixed Height Issue */}
          <div className="lg:col-span-4 2xl:col-span-5 ml-4">
            <div className="sticky top-6">
              <Card className="border-0 shadow-sm">
                <CardContent className="p-0">
                  <div className="px-4">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-2 rounded-xl">
                        <Settings className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                          Pengaturan
                        </h2>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Kelola preferensi Anda
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Fixed ScrollArea with proper height */}
                  <ScrollArea className="h-[calc(100vh-16rem)] px-2">
                    <div className="space-y-6 py-4">
                      {Object.entries(groupedItems).map(([category, items]) => (
                        <div key={category} className="space-y-2">
                          <div className="px-2">
                            <Badge
                              variant="secondary"
                              className={cn(
                                "text-xs font-medium px-2 py-1 rounded-md",
                                categoryColors[
                                  category as keyof typeof categoryColors
                                ]
                              )}
                            >
                              {
                                categoryLabels[
                                  category as keyof typeof categoryLabels
                                ]
                              }
                            </Badge>
                          </div>

                          <div className="space-y-1">
                            {items.map((item) => {
                              const NavItemContent = (
                                <Link
                                  href={item.href}
                                  className={cn(
                                    "group flex items-center gap-3 px-3 py-3 mx-2 rounded-xl transition-all duration-200 hover:scale-[1.02]",
                                    activeItem === item.href
                                      ? "bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/25"
                                      : "hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300"
                                  )}
                                  onClick={() => setActiveItem(item.href)}
                                >
                                  <div
                                    className={cn(
                                      "p-2 rounded-lg transition-colors flex-shrink-0",
                                      activeItem === item.href
                                        ? "bg-white/20"
                                        : "bg-gray-100 dark:bg-gray-800 group-hover:bg-gray-200 dark:group-hover:bg-gray-700"
                                    )}
                                  >
                                    <item.icon className="h-4 w-4" />
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium text-sm truncate">
                                      {item.title}
                                    </div>
                                    <div
                                      className={cn(
                                        "text-xs truncate mt-0.5 hidden 2xl:flex",
                                        activeItem === item.href
                                          ? "text-white/80"
                                          : "text-gray-500 dark:text-gray-400"
                                      )}
                                    >
                                      {item.description}
                                    </div>
                                  </div>
                                  <ChevronRight
                                    className={cn(
                                      "h-4 w-4 transition-transform flex-shrink-0",
                                      activeItem === item.href
                                        ? "rotate-90"
                                        : ""
                                    )}
                                  />
                                </Link>
                              );

                              if (item.roles) {
                                return (
                                  <PermissionCheck
                                    key={item.href}
                                    requiredRoles={item.roles}
                                  >
                                    {NavItemContent}
                                  </PermissionCheck>
                                );
                              }

                              return (
                                <div key={item.href}>{NavItemContent}</div>
                              );
                            })}
                          </div>

                          {category !== "support" && (
                            <div className="px-4">
                              <Separator className="my-2" />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-11 2xl:col-span-10">
            <Card className="border-0 shadow-sm min-h-[600px] py-0 mt-6 mb-4 mr-4 rounded-xl">
              <CardContent className="p-0">
                <div className="h-full">{children}</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
