"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Settings, Shield, Database, Upload } from "lucide-react";
import { GoogleProfilePictureManager } from "@/components/admin/GoogleProfilePictureManager";

const AdminToolsSettings: React.FC = () => {
  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <div className="bg-gradient-to-br from-orange-500 to-red-600 p-2 rounded-xl">
            <Settings className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Admin Tools
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Tools administratif dan maintenance untuk sistem
            </p>
          </div>
        </div>
      </div>

      <Separator />

      {/* Google Profile Picture Manager */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Upload className="h-5 w-5 text-blue-600" />
          <h2 className="text-lg font-semibold">Google Profile Picture Manager</h2>
        </div>
        <GoogleProfilePictureManager />
      </div>

      <Separator />

      {/* Future Admin Tools */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Database className="h-5 w-5 text-green-600" />
          <h2 className="text-lg font-semibold">Database Tools</h2>
        </div>
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Database Maintenance</CardTitle>
            <CardDescription>
              Tools untuk maintenance database dan optimasi performa.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Tools database maintenance akan ditambahkan di versi mendatang.
            </p>
          </CardContent>
        </Card>
      </div>

      <Separator />

      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-purple-600" />
          <h2 className="text-lg font-semibold">Security Tools</h2>
        </div>
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Security Audit</CardTitle>
            <CardDescription>
              Tools untuk audit keamanan dan monitoring sistem.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Tools security audit akan ditambahkan di versi mendatang.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminToolsSettings;
