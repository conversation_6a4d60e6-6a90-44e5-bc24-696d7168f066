"use client";

import { useState, useRef } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Upload,
  Camera,
  User,
  Sparkles,
  Edit3,
  ImageIcon,
  Trash2,
} from "lucide-react";
import { uploadProfileImage } from "@/actions/uploads/images";
import { toast } from "sonner";

interface ModernProfileImageProps {
  imageUrl: string;
  setImageUrl: (url: string) => void;
  name: string;
  username: string | null;
}

export default function ModernProfileImage({
  imageUrl,
  setImageUrl,
  name,
  username,
}: ModernProfileImageProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Handle file upload to S3
  const handleFileUpload = async (file: File) => {
    if (!file.type.startsWith("image/")) {
      toast.error("File harus berupa gambar");
      return;
    }

    setIsUploading(true);
    const toastId = toast.loading("Mengupload foto profil...");

    try {
      const formData = new FormData();
      formData.append("file", file);

      const result = await uploadProfileImage(formData);

      if (result.success && result.url) {
        setImageUrl(result.url);
        toast.success("Foto profil berhasil diupload!", { id: toastId });
      } else {
        toast.error(result.error || "Gagal mengupload foto profil", {
          id: toastId,
        });
      }
    } catch (error) {
      console.error("Error uploading profile image:", error);
      toast.error("Terjadi kesalahan saat mengupload foto profil", {
        id: toastId,
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Handle file upload
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      handleFileUpload(file);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      handleFileUpload(file);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const removeImage = () => {
    setImageUrl("");
  };

  const useRandomAvatar = () => {
    const randomId = Math.floor(Math.random() * 70) + 1;
    setImageUrl(`https://i.pravatar.cc/300?img=${randomId}`);
  };

  return (
    <Card className="border-0 shadow-xl bg-gradient-to-br from-white/90 to-gray-50/90 dark:from-gray-800/90 dark:to-gray-900/90 backdrop-blur-sm">
      <CardContent className="p-4">
        <div className="flex flex-col items-center space-y-6">
          {/* Profile Avatar with Hover Effects */}
          <div
            className="relative group cursor-pointer"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={triggerFileInput}
          >
            <Avatar className="h-40 w-40 border-4 border-white shadow-2xl ring-4 ring-indigo-500/20 transition-all duration-300 group-hover:ring-indigo-500/40 group-hover:scale-105">
              <AvatarImage
                src={imageUrl || `https://i.pravatar.cc/300?img=1`}
                alt="Profile"
                className="object-cover transition-all duration-300 group-hover:brightness-75"
              />
              <AvatarFallback className="bg-gradient-to-br from-indigo-500 to-purple-600 text-white text-4xl font-bold">
                {name?.charAt(0) || "U"}
              </AvatarFallback>
            </Avatar>

            {/* Overlay on Hover */}
            <div
              className={`absolute inset-0 bg-black/50 rounded-full flex items-center justify-center transition-all duration-300 ${
                isHovering || isDragging ? "opacity-100" : "opacity-0"
              }`}
            >
              <div className="text-white text-center">
                <Camera className="h-8 w-8 mx-auto mb-2" />
                <p className="text-sm font-medium">
                  {isDragging ? "Lepas untuk upload" : "Ganti Foto"}
                </p>
              </div>
            </div>

            {/* Status Badge */}
            <div className="absolute -top-2 -right-2">
              <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg px-3 py-1">
                <Sparkles className="h-3 w-3 mr-1" />
                Online
              </Badge>
            </div>
          </div>

          {/* User Info */}
          <div className="text-center space-y-2">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {name || "Nama Pengguna"}
            </h3>
            <p className="text-lg text-indigo-600 dark:text-indigo-400 font-medium">
              {username ? `@${username}` : "@username"}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3 justify-center">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
            />

            <Button
              onClick={triggerFileInput}
              disabled={isUploading}
              className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg transition-all duration-200 hover:shadow-xl hover:scale-105 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              size="sm"
            >
              {isUploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Mengupload...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Foto
                </>
              )}
            </Button>

            <Button
              onClick={useRandomAvatar}
              variant="outline"
              className="border-indigo-200 text-indigo-600 hover:bg-indigo-50 dark:border-indigo-800 dark:text-indigo-400 dark:hover:bg-indigo-900/20 shadow-md transition-all duration-200 hover:shadow-lg cursor-pointer"
              size="sm"
            >
              <User className="h-4 w-4 mr-2" />
              Avatar Acak
            </Button>

            {imageUrl && (
              <Button
                onClick={removeImage}
                variant="outline"
                className="border-red-200 text-red-600 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20 shadow-md transition-all duration-200 hover:shadow-lg cursor-pointer"
                size="sm"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Hapus
              </Button>
            )}
          </div>

          {/* Upload Tips */}
          <div className="text-center text-sm text-gray-500 dark:text-gray-400 space-y-1">
            <p className="flex items-center justify-center gap-2">
              <ImageIcon className="h-4 w-4" />
              Drag & drop atau klik untuk upload
            </p>
            <p>Format: JPG, PNG, GIF (Max 5MB)</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
