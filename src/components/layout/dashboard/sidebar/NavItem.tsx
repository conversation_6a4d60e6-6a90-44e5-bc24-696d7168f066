"use client";

import React, { useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { classNames } from "./SidebarNavigation";

// Custom NavLink component
interface NavLinkProps {
  href: string;
  displayHref?: string;
  children: React.ReactNode;
  className?: string;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
  "aria-current"?:
    | "page"
    | "step"
    | "location"
    | "date"
    | "time"
    | "true"
    | "false";
  "aria-disabled"?: boolean;
}

const NavLink: React.FC<NavLinkProps> = ({
  href,
  displayHref,
  children,
  className,
  onClick,
  ...props
}) => {
  const router = useRouter();

  const handleMouseEnter = (e: React.MouseEvent<HTMLAnchorElement>) => {
    // Change the href attribute for hover display
    if (displayHref) {
      e.currentTarget.setAttribute("href", displayHref);
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLAnchorElement>) => {
    // Restore the actual href
    e.currentTarget.setAttribute("href", href);
  };

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    if (onClick) {
      onClick(e);
    }
    router.push(href);
  };

  return (
    <a
      href={href}
      className={className}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      {...props}
    >
      {children}
    </a>
  );
};

interface NavItemProps {
  item: {
    name: string;
    href: string;
    displayHref?: string;
    icon: React.ElementType;
    roles?: string[];
    hasChildren?: boolean;
    children?: any[];
  };
  isCollapsed: boolean;
  onItemClick?: () => void;
}

const NavItem: React.FC<NavItemProps> = ({
  item,
  isCollapsed,
  onItemClick,
}) => {
  const pathname = usePathname();
  const [isNavigating, setIsNavigating] = useState(false);

  // Enhanced active link detection
  const isItemActive = (currentItem: any, currentPath: string): boolean => {
    // If item has children, check if any child is active
    if (currentItem.hasChildren && currentItem.children) {
      return currentItem.children.some((child: any) =>
        isItemActive(child, currentPath)
      );
    }

    // For items without children, check direct path match
    const itemPath = currentItem.href;
    const displayPath =
      currentItem.displayHref || itemPath.replace("/dashboard", "");

    // Exact match check
    if (currentPath === itemPath || currentPath === displayPath) {
      return true;
    }

    // For dashboard paths, also check if current path matches the full href
    if (currentPath === `/dashboard${displayPath}`) {
      return true;
    }

    // Check if current path starts with the display path (for nested routes)
    if (displayPath !== "/" && currentPath.startsWith(displayPath)) {
      const remainingPath = currentPath.substring(displayPath.length);
      // Make sure it's a proper sub-path (starts with / or is empty)
      return remainingPath === "" || remainingPath.startsWith("/");
    }

    return false;
  };

  // Check if this item is active
  const isCurrent = isItemActive(item, pathname);

  // Handle navigation with loading state
  const handleNavigation = async (e: React.MouseEvent) => {
    // Don't navigate if already navigating or if it's the current page
    if (isNavigating || isCurrent) {
      e.preventDefault();
      return;
    }

    // Let the link click be handled naturally by the browser and intercepted by useUnsavedChangesWarning hook
    // Only prevent default if we need to show loading state
    setIsNavigating(true);
    onItemClick?.();

    // Reset loading state after a delay
    setTimeout(() => setIsNavigating(false), 1000);
  };

  return (
    <div>
      {isCollapsed ? (
        <Tooltip>
          <TooltipTrigger asChild>
            <NavLink
              href={item.href}
              displayHref={item.displayHref}
              onClick={handleNavigation}
              className={classNames(
                isCurrent
                  ? "bg-blue-600 text-white shadow-md dark:bg-blue-500 dark:text-white"
                  : "text-gray-700 hover:bg-gray-200 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white",
                "group flex items-center rounded-md px-2 py-2 text-sm font-medium transition-all duration-500 ease-in-out",
                isNavigating ? "opacity-50 cursor-not-allowed" : ""
              )}
              aria-current={isCurrent ? "page" : undefined}
              aria-disabled={isNavigating}
            >
              {/* Icon container - always fixed width */}
              <div className="w-6 flex-shrink-0 flex justify-center">
                <item.icon
                  className={classNames(
                    isCurrent
                      ? "text-white dark:text-white"
                      : "text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300",
                    "h-6 w-6 transition-all duration-500 ease-in-out"
                  )}
                  aria-hidden="true"
                />
              </div>

              {/* Text container - transitions opacity and width */}
              <div
                className={classNames(
                  "ml-3 transition-all duration-500 ease-in-out overflow-hidden",
                  "opacity-0 w-0"
                )}
              >
                <span className="truncate">{item.name}</span>
              </div>
            </NavLink>
          </TooltipTrigger>
          <TooltipContent side="right" sideOffset={5}>
            <p>{item.name}</p>
          </TooltipContent>
        </Tooltip>
      ) : (
        <NavLink
          href={item.href}
          displayHref={item.displayHref}
          onClick={handleNavigation}
          className={classNames(
            isCurrent
              ? "bg-blue-600 text-white shadow-md dark:bg-blue-500 dark:text-white"
              : "text-gray-700 hover:bg-gray-200 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white",
            "group flex items-center rounded-md px-2 py-2 text-sm font-medium transition-all duration-500 ease-in-out",
            isNavigating ? "opacity-50 cursor-not-allowed" : ""
          )}
          aria-current={isCurrent ? "page" : undefined}
          aria-disabled={isNavigating}
        >
          {/* Icon container - always fixed width */}
          <div className="w-6 flex-shrink-0 flex justify-center">
            <item.icon
              className={classNames(
                isCurrent
                  ? "text-white dark:text-white"
                  : "text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300",
                "h-6 w-6 transition-all duration-500 ease-in-out"
              )}
              aria-hidden="true"
            />
          </div>

          {/* Text container */}
          <div className="ml-3 flex-1 transition-all duration-500 ease-in-out">
            <span className="truncate">{item.name}</span>
          </div>
        </NavLink>
      )}
    </div>
  );
};

export default NavItem;
