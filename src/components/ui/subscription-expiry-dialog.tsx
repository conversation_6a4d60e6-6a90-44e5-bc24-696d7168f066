"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>ert<PERSON>rian<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ArrowRight } from "lucide-react";

interface SubscriptionExpiryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const SubscriptionExpiryDialog: React.FC<
  SubscriptionExpiryDialogProps
> = ({ open, onOpenChange }) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleUpgrade = async () => {
    setIsLoading(true);
    // Add a small delay for better UX
    setTimeout(() => {
      onOpenChange(false);
      router.push("/dashboard/settings/plans");
      setIsLoading(false);
    }, 300);
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg max-w-[95vw] p-0 overflow-hidden bg-gradient-to-br from-white via-blue-50/80 to-purple-50/70 dark:from-gray-900 dark:via-gray-800 dark:to-gray-800 rounded-2xl shadow-2xl border-0 ring-1 ring-black/5 dark:ring-white/10">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse" />
          <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-gradient-to-tr from-orange-400/10 to-pink-400/10 rounded-full blur-2xl animate-pulse delay-1000" />
        </div>

        <div className="relative p-6 sm:p-8">
          <DialogHeader className="space-y-4">
            {/* Icon with enhanced styling */}
            <div className="flex justify-center">
              <div className="relative flex h-16 w-16 sm:h-20 sm:w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-orange-100 via-orange-50 to-yellow-50 dark:from-orange-900/30 dark:via-orange-800/20 dark:to-yellow-900/10 ring-1 ring-orange-200/50 dark:ring-orange-700/30 shadow-lg">
                <AlertTriangle className="h-8 w-8 sm:h-10 sm:w-10 text-orange-600 dark:text-orange-400 drop-shadow-sm" />
                <div className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full animate-ping" />
                <div className="absolute -top-1 -right-1 h-4 w-4 bg-red-600 rounded-full" />
              </div>
            </div>

            <div className="text-center space-y-3">
              <DialogTitle className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-gray-50 dark:via-white dark:to-gray-50 bg-clip-text text-transparent leading-tight">
                Langganan Anda Telah Berakhir
              </DialogTitle>
              <DialogDescription className="text-sm sm:text-base text-gray-600 dark:text-gray-300 leading-relaxed max-w-md mx-auto">
                Perpanjang langganan untuk dapat kembali membuat transaksi dan
                mengakses semua fitur premium yang tersedia.
              </DialogDescription>
            </div>
          </DialogHeader>

          {/* Feature highlights */}
          <div className="mt-6 p-4 rounded-xl bg-white/80 dark:bg-gray-800/50 border border-gray-200/60 dark:border-gray-700/60 backdrop-blur-sm">
            <div className="flex items-center gap-3 text-sm text-gray-700 dark:text-gray-300">
              <Sparkles className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
              <span className="font-medium">Akses semua fitur premium</span>
            </div>
            <div className="flex items-center gap-3 text-sm text-gray-700 dark:text-gray-300 mt-2">
              <Clock className="h-4 w-4 text-green-600 dark:text-green-400 flex-shrink-0" />
              <span className="font-medium">Transaksi tanpa batas</span>
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-3 bg-gradient-to-r from-gray-50/80 via-white/40 to-gray-50/80 dark:from-gray-900/50 dark:via-gray-800/30 dark:to-gray-900/50 backdrop-blur-sm px-6 sm:px-8 py-6 border-t border-gray-200/60 dark:border-gray-700/40">
          <Button
            variant="outline"
            onClick={handleClose}
            className="order-2 sm:order-1 w-full sm:w-auto bg-white/80 dark:bg-gray-800/80 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700/80 backdrop-blur-sm transition-all duration-200 font-medium cursor-pointer"
          >
            Nanti Saja
          </Button>
          <Button
            onClick={handleUpgrade}
            disabled={isLoading}
            className="order-1 sm:order-2 w-full sm:w-auto bg-gradient-to-r from-blue-600 via-blue-600 to-purple-600 hover:from-blue-700 hover:via-blue-700 hover:to-purple-700 text-white font-semibold shadow-lg hover:shadow-xl active:scale-[0.98] transition-all duration-200 group disabled:opacity-70 disabled:cursor-not-allowed disabled:hover:shadow-lg disabled:active:scale-100 cursor-pointer"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                <span>Memproses...</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <span>Perpanjang Sekarang</span>
                <ArrowRight className="h-4 w-4 group-hover:translate-x-0.5 transition-transform duration-200" />
              </div>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Enhanced hook with better state management
export const useSubscriptionExpiryDialog = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);
  const { data: session, status } = useSession();

  useEffect(() => {
    const checkSubscriptionExpiry = async () => {
      // Skip if already checked, no session, or dialog already shown
      if (
        hasChecked ||
        status === "loading" ||
        !session?.user?.id ||
        typeof window === "undefined"
      ) {
        return;
      }

      // Check if dialog was already shown in this session
      const dialogShown = sessionStorage.getItem(
        "subscriptionExpiryDialogShown"
      );
      if (dialogShown) {
        setHasChecked(true);
        return;
      }

      setIsLoading(true);

      try {
        const response = await fetch("/api/subscription/expiry", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.expired) {
            setIsOpen(true);
            sessionStorage.setItem("subscriptionExpiryDialogShown", "true");
          }
        } else {
          console.warn(
            "Failed to check subscription status:",
            response.statusText
          );
        }
      } catch (error) {
        console.error("Error checking subscription expiry:", error);
      } finally {
        setIsLoading(false);
        setHasChecked(true);
      }
    };

    checkSubscriptionExpiry();
  }, [session?.user?.id, status, hasChecked]);

  // Reset check status when session changes
  useEffect(() => {
    if (status !== "loading") {
      setHasChecked(false);
    }
  }, [session?.user?.id, status]);

  const closeDialog = () => {
    setIsOpen(false);
  };

  const resetDialogState = () => {
    if (typeof window !== "undefined") {
      sessionStorage.removeItem("subscriptionExpiryDialogShown");
      setHasChecked(false);
    }
  };

  return {
    isOpen,
    setIsOpen,
    closeDialog,
    resetDialogState,
    isLoading,
  };
};
