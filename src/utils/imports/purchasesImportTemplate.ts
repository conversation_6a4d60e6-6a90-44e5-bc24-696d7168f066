// Purchase import template generation utilities
// Creates standardized Excel import templates for purchase transactions
// Based on the same structure as the export functionality

import * as XLSX from "xlsx-js-style";

// --- INTERFACES (matching export structure) ---

interface ColumnConfig {
  key: string;
  label: string;
  type: string;
  width?: number; // Custom width in characters
  formatter?: (value: any) => any;
}

// --- UTILITY FUNCTIONS (matching export structure) ---

const applyCellStyleLocal = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCellsLocal = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidthsLocal = (
  ws: XLSX.WorkSheet,
  widths: { wch: number }[]
) => {
  ws["!cols"] = widths;
};

const setRowHeightsLocal = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

// --- COLUMN CONFIGURATION (matching export format except transactionNumber) ---

const getImportTemplateColumnConfig = (): ColumnConfig[] => [
  { key: "purchaseDate", label: "Tanggal Pembelian", type: "date", width: 18 },
  { key: "invoiceRef", label: "No. Invoice", type: "text", width: 15 },
  { key: "supplier.name", label: "Nama Supplier", type: "text", width: 25 },
  { key: "supplier.phone", label: "Nomor Telepon", type: "text", width: 16 },
  { key: "totalAmount", label: "Total Pembelian", type: "currency", width: 18 },
  { key: "paymentDueDate", label: "Jatuh Tempo", type: "date", width: 16 },
  {
    key: "status",
    label: "Status Pembayaran",
    type: "text",
    width: 18,
    formatter: (value: string) => (value === "LUNAS" ? "Lunas" : "Belum Lunas"),
  },
  { key: "memo", label: "Memo", type: "text", width: 30 },
  {
    key: "isDraft",
    label: "Status Draft",
    type: "text",
    width: 14,
    formatter: (isDraft: boolean) => (isDraft ? "Draft" : "Selesai"),
  },
  { key: "item.product.name", label: "Nama Produk", type: "text", width: 25 },
  { key: "item.product.sku", label: "SKU", type: "text", width: 12 },
  { key: "item.quantity", label: "Quantity", type: "number", width: 12 },
  {
    key: "item.costAtPurchase",
    label: "Harga Beli",
    type: "currency",
    width: 16,
  },
  {
    key: "item.discountPercentage",
    label: "Diskon (%)",
    type: "number",
    width: 12,
  },
  {
    key: "item.discountAmount",
    label: "Diskon (Rp)",
    type: "currency",
    width: 16,
  },
  { key: "item.unit", label: "Satuan", type: "text", width: 10 },
];

// --- STYLING CONSTANTS (fallback if shared styles not available) ---

const TEMPLATE_CELL_STYLES = {
  sheetTitle: {
    font: { bold: true, size: 16, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "2563EB" } }, // Blue background
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } },
    },
  },
  sheetSubtitle: {
    font: { bold: false, size: 12, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F3F4F6" } }, // Light gray background
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } },
    },
  },
  tableHeader: {
    font: { bold: true, size: 11, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "059669" } }, // Green background
    alignment: { horizontal: "center", vertical: "center", wrapText: true },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } },
    },
  },
  sampleDataEven: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "FFFFFF" } }, // White background
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "D1D5DB" } },
      bottom: { style: "thin", color: { rgb: "D1D5DB" } },
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
  sampleDataOdd: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F9FAFB" } }, // Very light gray background
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "D1D5DB" } },
      bottom: { style: "thin", color: { rgb: "D1D5DB" } },
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
};

// --- MAIN TEMPLATE CREATION FUNCTION ---

/**
 * Creates a professional import template for purchases with structure matching export
 */
export const createPurchaseImportTemplate = (): XLSX.WorkBook => {
  console.log("[TEMPLATE] Creating professional purchase import template");

  const workbook = XLSX.utils.book_new();
  const columns = getImportTemplateColumnConfig();
  const headerRowCount = 4; // Title (1) + Subtitle (2) + Empty (3) + Headers (4)

  // --- Sheet Title and Structure ---
  const sheetTitle = "TEMPLATE IMPORT PEMBELIAN";
  const sheetSubtitle = "KivaPOS - Sistem Manajemen Pembelian";

  // Create template data structure (matching export format)
  const templateData = [
    // Row 1: Main title
    [sheetTitle],
    // Row 2: Subtitle
    [sheetSubtitle],
    // Row 3: Empty row for spacing
    [],
    // Row 4: Column headers
    columns.map((col) => col.label),
    // Row 5-6: Sample data rows for guidance
    [
      "2024-01-15", // Tanggal Pembelian
      "INV-001", // No. Invoice
      "PT Supplier ABC", // Nama Supplier
      "021-12345678", // Nomor Telepon
      47000, // Total Pembelian
      "2024-02-15", // Jatuh Tempo
      "Belum Lunas", // Status Pembayaran
      "Pembelian rutin", // Memo
      "Draft", // Status Draft
      "Produk A", // Nama Produk
      "SKU001", // SKU
      10, // Quantity
      45000, // Harga Beli
      5, // Diskon (%)
      2000, // Diskon (Rp)
      "Pcs", // Satuan
    ],
    [
      "2024-01-16", // Tanggal Pembelian
      "INV-002", // No. Invoice
      "CV Supplier XYZ", // Nama Supplier
      "021-87654321", // Nomor Telepon
      65000, // Total Pembelian
      "2024-02-16", // Jatuh Tempo
      "Lunas", // Status Pembayaran
      "", // Memo
      "Selesai", // Status Draft
      "Produk B", // Nama Produk
      "SKU002", // SKU
      5, // Quantity
      65000, // Harga Beli
      0, // Diskon (%)
      0, // Diskon (Rp)
      "Pcs", // Satuan
    ],
  ];

  // Create worksheet from template data
  const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

  console.log("[TEMPLATE] Applied basic sheet structure");

  // --- APPLY PROFESSIONAL STYLING ---

  // 1. Style main title (Row 1) - merge across all columns and apply style
  const titleRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}1`;
  mergeCellsLocal(templateSheet, titleRange);
  applyCellStyleLocal(templateSheet, "A1", TEMPLATE_CELL_STYLES.sheetTitle);

  // 2. Style subtitle (Row 2) - merge across all columns and apply style
  const subtitleRange = `A2:${XLSX.utils.encode_col(columns.length - 1)}2`;
  mergeCellsLocal(templateSheet, subtitleRange);
  applyCellStyleLocal(templateSheet, "A2", TEMPLATE_CELL_STYLES.sheetSubtitle);

  // 3. Style column headers (Row 4) with professional header styling
  columns.forEach((col, index) => {
    const headerCell = XLSX.utils.encode_cell({
      r: headerRowCount - 1,
      c: index,
    });
    applyCellStyleLocal(
      templateSheet,
      headerCell,
      TEMPLATE_CELL_STYLES.tableHeader
    );

    // Add number formatting fair currency and number columns
    if (col.type === "currency") {
      if (!templateSheet[headerCell].s) templateSheet[headerCell].s = {};
      templateSheet[headerCell].s.numFmt = '"Rp "#,##0_);("Rp "#,##0)';
    }
  });

  // 4. Style sample data rows (Row 5-6) with alternating colors
  const sampleDataRowCount = 2;
  for (let rowIndex = 0; rowIndex < sampleDataRowCount; rowIndex++) {
    const actualRowIndex = headerRowCount + rowIndex;
    const isEvenRow = rowIndex % 2 === 0;

    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({
        r: actualRowIndex,
        c: colIndex,
      });
      const style = isEvenRow
        ? TEMPLATE_CELL_STYLES.sampleDataEven
        : TEMPLATE_CELL_STYLES.sampleDataOdd;
      applyCellStyleLocal(templateSheet, cellRef, style);

      // Apply number formatting for currency columns in sample data
      if (col.type === "currency" && templateSheet[cellRef]) {
        if (!templateSheet[cellRef].s) templateSheet[cellRef].s = { ...style };
        templateSheet[cellRef].s.numFmt = '"Rp "#,##0_);("Rp "#,##0)';
      }
    });
  }

  console.log("[TEMPLATE] Applied professional styling to all elements");

  // --- SET COLUMN WIDTHS (matching export system) ---
  const colWidths = columns.map((col) => ({ wch: col.width || 20 }));
  setColumnWidthsLocal(templateSheet, colWidths);

  // --- SET ROW HEIGHTS (matching export system) ---
  setRowHeightsLocal(templateSheet, {
    1: 25, // Title row height
    2: 20, // Subtitle row height
    3: 15, // Empty row height
    4: 35, // Header row height
  });

  console.log("[TEMPLATE] Set column widths and row heights");

  // Add the main template sheet to workbook
  XLSX.utils.book_append_sheet(workbook, templateSheet, "Template Pembelian");

  // --- CREATE INSTRUCTIONS SHEET ---
  const instructionsData = [
    ["PETUNJUK PENGGUNAAN TEMPLATE IMPORT PEMBELIAN"],
    ["KivaPOS - Panduan Lengkap Import Data Pembelian"],
    [],
    ["1. KOLOM WAJIB (Harus diisi):"],
    [""],
    ["   • Tanggal Pembelian: Format YYYY-MM-DD (contoh: 2024-01-15)"],
    [
      "   • Nama Produk: Nama produk yang dibeli (harus sudah ada di sistem - case sensitive)",
    ],
    ["   • Quantity: Jumlah produk yang dibeli (angka positif)"],
    ["   • Harga Beli: Harga per unit (angka, tanpa titik/koma)"],
    [],
    ["2. KOLOM OPSIONAL:"],
    [""],
    ["   • No. Invoice: Nomor invoice (kosongkan untuk auto-generate)"],
    ["   • Nama Supplier: Nama pemasok (akan dibuat otomatis jika belum ada)"],
    ["   • Nomor Telepon: Nomor telepon pemasok"],
    ["   • Total Pembelian: Total harga pembelian (angka, tanpa titik/koma)"],
    ["   • Jatuh Tempo: Format YYYY-MM-DD (contoh: 2024-02-15)"],
    ["   • Status Pembayaran: 'Lunas' atau 'Belum Lunas'"],
    ["   • Memo: Catatan tambahan untuk pembelian"],
    ["   • Status Draft: 'Draft' atau 'Selesai'"],
    ["   • SKU: Kode SKU produk"],
    ["   • Diskon (%): Persentase diskon (angka, contoh: 5 untuk 5%)"],
    ["   • Diskon (Rp): Jumlah diskon dalam Rupiah (angka)"],
    ["   • Satuan: Unit produk (default: Pcs)"],
    [],
    ["3. FORMAT DATA:"],
    [""],
    ["   • Tanggal: Gunakan format YYYY-MM-DD (contoh: 2024-01-15)"],
    ["   • Angka: Masukkan tanpa titik/koma (contoh: 45000 bukan 45.000)"],
    ["   • Status: Hanya 'Lunas' atau 'Belum Lunas' (case sensitive)"],
    ["   • Status Draft: Hanya 'Draft' atau 'Selesai' (case sensitive)"],
    ["   • Text: Hindari karakter khusus yang tidak perlu"],
    [],
    ["4. ATURAN KHUSUS:"],
    [""],
    [
      "   • Nama Produk: Harus sama persis dengan yang ada di sistem (case-sensitive)",
    ],
    ["   • Maksimal 100 transaksi per import"],
    ["   • Produk harus sudah ada di sistem sebelum import"],
    ["   • Supplier akan dibuat otomatis jika belum ada"],
    ["   • Invoice yang duplikat akan ditolak (akan ada konfirmasi)"],
    [],
    ["5. TIPS IMPORT:"],
    [""],
    ["   • Hapus baris contoh sebelum import data sesungguhnya"],
    ["   • Pastikan tidak ada baris kosong di tengah data"],
    ["   • Periksa format tanggal sebelum upload"],
    ["   • Simpan file dalam format .xlsx atau .xls"],
    ["   • Backup data sebelum melakukan import"],
    [],
    ["6. MENANGANI MERGED CELLS:"],
    [""],
    ["   • Jika satu invoice memiliki banyak produk:"],
    ["     - Isi data invoice (tanggal, supplier, dll) di baris pertama"],
    ["     - Baris selanjutnya bisa dikosongkan untuk kolom yang sama"],
    [
      "     - Sistem akan otomatis mengisi data yang kosong dari baris sebelumnya",
    ],
    [],
    ["7. TROUBLESHOOTING:"],
    [""],
    ["   • Import gagal? Periksa:"],
    ["     - Format tanggal (YYYY-MM-DD)"],
    ["     - Nama produk harus sama persis dengan di sistem"],
    ["     - Format angka (tanpa titik/koma)"],
    ["     - Status pembayaran: 'Lunas' atau 'Belum Lunas'"],
    ["     - Status Draft: 'Draft' atau 'Selesai'"],
    ["     - Tidak ada baris kosong di tengah data"],
    [],
    ["   • Konflik invoice? Pilih:"],
    ["     - 'Ya, Lanjutkan' untuk skip data yang conflict"],
    ["     - 'Batal' untuk periksa dan perbaiki data manual"],
    [],
    ["8. CONTOH DATA YANG BENAR:"],
    [""],
    [
      "   2024-01-15 | INV001 | PT ABC | 021-123 | 47000 | 2024-02-15 | Lunas | Catatan | Draft | Produk A | SKU001 | 10 | 50000 | 5 | 0 | Pcs",
    ],
    [
      "   2024-01-15 | INV001 | PT ABC |        | 47000 | 2024-02-15 | Lunas | Catatan | Draft | Produk B | SKU002 | 5  | 30000 | 0 | 1000 | Pcs",
    ],
    [],
    ["   ^ Contoh di atas menunjukkan 1 invoice dengan 2 produk"],
    ["   ^ Baris kedua tidak perlu mengulang data supplier dan invoice"],
  ];

  const instructionsSheet = XLSX.utils.aoa_to_sheet(instructionsData);

  // Style the instructions sheet
  applyCellStyleLocal(instructionsSheet, "A1", TEMPLATE_CELL_STYLES.sheetTitle);
  applyCellStyleLocal(
    instructionsSheet,
    "A2",
    TEMPLATE_CELL_STYLES.sheetSubtitle
  );

  // Merge title and subtitle
  mergeCellsLocal(instructionsSheet, "A1:A1");
  mergeCellsLocal(instructionsSheet, "A2:A2");

  // Set column width for instructions
  setColumnWidthsLocal(instructionsSheet, [{ wch: 120 }]); // Wide column for instructions

  // Set row heights
  setRowHeightsLocal(instructionsSheet, { 1: 25, 2: 20 });

  XLSX.utils.book_append_sheet(
    workbook,
    instructionsSheet,
    "Petunjuk Penggunaan"
  );

  console.log("[TEMPLATE] Created comprehensive instructions sheet");
  console.log("[TEMPLATE] Purchase import template created successfully");

  return workbook;
};

// --- EXPORT FOR COMPATIBILITY ---

/**
 * Legacy export function name for backward compatibility
 */
export const createPurchasesImportTemplate = createPurchaseImportTemplate;
