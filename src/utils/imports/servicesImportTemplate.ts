// Service import template generation utilities
// Creates standardized Excel import templates for service management

import * as XLSX from "xlsx-js-style";

// --- INTERFACES (self-contained) ---
interface ColumnConfig {
  key: string;
  label: string;
  type: string;
  width?: number; // Custom width in characters
}

// --- UTILITY FUNCTIONS (self-contained) ---

const applyCellStyleLocal = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCellsLocal = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidthsLocal = (
  ws: XLSX.WorkSheet,
  widths: { wch: number }[]
) => {
  ws["!cols"] = widths;
};

const setRowHeightsLocal = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

// --- STYLING CONSTANTS (self-contained) ---

const TEMPLATE_CELL_STYLES = {
  sheetTitle: {
    font: { bold: true, size: 16, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "2563EB" } },
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  },
  sheetSubtitle: {
    font: { bold: false, size: 12, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F3F4F6" } },
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  },
  tableHeader: {
    font: { bold: true, size: 11, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "059669" } },
    alignment: { horizontal: "center", vertical: "center", wrapText: true },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  },
  sampleDataEven: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "FFFFFF" } },
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "D1D5DB" } },
      bottom: { style: "thin", color: { rgb: "D1D5DB" } },
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
  sampleDataOdd: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F9FAFB" } },
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "D1D5DB" } },
      bottom: { style: "thin", color: { rgb: "D1D5DB" } },
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
};

// --- COLUMN CONFIGURATION ---

const getImportTemplateColumnConfig = (): ColumnConfig[] => [
  { key: "receivedDate", label: "Tanggal Diterima*", type: "date", width: 15 },
  { key: "customerName", label: "Nama Pelanggan*", type: "text", width: 20 },
  { key: "customerPhone", label: "Telepon Pelanggan", type: "text", width: 15 },
  { key: "customerEmail", label: "Email Pelanggan", type: "text", width: 25 },
  { key: "deviceType", label: "Jenis Perangkat*", type: "text", width: 15 },
  { key: "deviceBrand", label: "Merek Perangkat", type: "text", width: 15 },
  { key: "deviceModel", label: "Model Perangkat", type: "text", width: 20 },
  { key: "complaint", label: "Keluhan*", type: "text", width: 30 },
  {
    key: "estimatedCost",
    label: "Estimasi Biaya",
    type: "currency",
    width: 12,
  },
  {
    key: "warrantyPeriod",
    label: "Periode Garansi (hari)",
    type: "number",
    width: 12,
  },
  { key: "status", label: "Status", type: "text", width: 15 },
  { key: "notes", label: "Catatan", type: "text", width: 25 },
];

/**
 * Creates a professional import template for services
 */
export const createServiceImportTemplate = (): XLSX.WorkBook => {
  console.log("[TEMPLATE] Creating professional service import template");

  const workbook = XLSX.utils.book_new();
  const columns = getImportTemplateColumnConfig();
  const headerRowCount = 4;

  const sheetTitle = "TEMPLATE IMPORT SERVIS";
  const sheetSubtitle = "KivaPOS - Sistem Manajemen Servis";

  const templateData = [
    [sheetTitle],
    [sheetSubtitle],
    [],
    columns.map((col) => col.label),
    // Sample data rows
    [
      "2024-01-15",
      "John Doe",
      "081234567890",
      "<EMAIL>",
      "Laptop",
      "ASUS",
      "VivoBook X441",
      "Laptop tidak bisa menyala",
      250000,
      30,
      "Diterima",
      "Perlu pengecekan power supply",
    ],
    [
      "2024-01-16",
      "Jane Smith",
      "081234567891",
      "<EMAIL>",
      "Smartphone",
      "Samsung",
      "Galaxy A52",
      "Layar pecah",
      150000,
      14,
      "Dalam Proses",
      "Ganti layar LCD",
    ],
  ];

  const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

  const titleRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}1`;
  mergeCellsLocal(templateSheet, titleRange);
  applyCellStyleLocal(templateSheet, "A1", TEMPLATE_CELL_STYLES.sheetTitle);

  const subtitleRange = `A2:${XLSX.utils.encode_col(columns.length - 1)}2`;
  mergeCellsLocal(templateSheet, subtitleRange);
  applyCellStyleLocal(templateSheet, "A2", TEMPLATE_CELL_STYLES.sheetSubtitle);

  columns.forEach((col, index) => {
    const headerCell = XLSX.utils.encode_cell({
      r: headerRowCount - 1,
      c: index,
    });
    applyCellStyleLocal(
      templateSheet,
      headerCell,
      TEMPLATE_CELL_STYLES.tableHeader
    );
  });

  const sampleDataRowCount = 2;
  for (let rowIndex = 0; rowIndex < sampleDataRowCount; rowIndex++) {
    const actualRowIndex = headerRowCount + rowIndex;
    const isEvenRow = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({
        r: actualRowIndex,
        c: colIndex,
      });
      const style = isEvenRow
        ? TEMPLATE_CELL_STYLES.sampleDataEven
        : TEMPLATE_CELL_STYLES.sampleDataOdd;
      applyCellStyleLocal(templateSheet, cellRef, style);
      if (col.type === "currency" && templateSheet[cellRef]) {
        if (!templateSheet[cellRef].s) templateSheet[cellRef].s = { ...style };
        templateSheet[cellRef].s.numFmt = '"Rp "#,##0_);("Rp "#,##0)';
      }
    });
  }

  const colWidths = columns.map((col) => ({ wch: col.width || 20 }));
  setColumnWidthsLocal(templateSheet, colWidths);
  setRowHeightsLocal(templateSheet, { 1: 25, 2: 20, 3: 15, 4: 35 });

  XLSX.utils.book_append_sheet(workbook, templateSheet, "Template Servis");

  const instructionsData = [
    ["PETUNJUK PENGGUNAAN TEMPLATE IMPORT SERVIS"],
    ["KivaPOS - Panduan Lengkap Import Data Servis"],
    [],
    ["1. KOLOM WAJIB (Ditandai dengan *):"],
    ["   • Tanggal Diterima: Format YYYY-MM-DD"],
    ["   • Nama Pelanggan: Nama pemilik perangkat"],
    ["   • Jenis Perangkat: Contoh: Laptop, Smartphone"],
    ["   • Keluhan: Deskripsi masalah"],
    [],
    ["2. STATUS YANG VALID:"],
    ["   • Diterima, Dalam Proses, Selesai, Diambil (case sensitive)"],
    [],
    ["3. TIPS IMPORT:"],
    ["   • Pelanggan akan dibuat otomatis jika belum ada."],
    ["   • Hapus baris contoh sebelum mengupload."],
  ];

  const instructionsSheet = XLSX.utils.aoa_to_sheet(instructionsData);
  applyCellStyleLocal(instructionsSheet, "A1", TEMPLATE_CELL_STYLES.sheetTitle);
  applyCellStyleLocal(
    instructionsSheet,
    "A2",
    TEMPLATE_CELL_STYLES.sheetSubtitle
  );
  mergeCellsLocal(instructionsSheet, `A1:${XLSX.utils.encode_col(0)}1`);
  mergeCellsLocal(instructionsSheet, `A2:${XLSX.utils.encode_col(0)}2`);
  setColumnWidthsLocal(instructionsSheet, [{ wch: 120 }]);
  setRowHeightsLocal(instructionsSheet, { 1: 25, 2: 20 });
  XLSX.utils.book_append_sheet(
    workbook,
    instructionsSheet,
    "Petunjuk Penggunaan"
  );

  console.log("[TEMPLATE] Service import template created successfully");
  return workbook;
};
