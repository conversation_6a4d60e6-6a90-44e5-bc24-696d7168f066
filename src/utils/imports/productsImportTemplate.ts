// Product import template generation utilities
// Creates standardized Excel import templates for products

import * as XLSX from "xlsx-js-style";

// --- INTERFACES (self-contained) ---
interface ColumnConfig {
  key: string;
  label: string;
  type: string;
  width?: number; // Custom width in characters
}

// --- UTILITY FUNCTIONS (self-contained) ---

const applyCellStyleLocal = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCellsLocal = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidthsLocal = (
  ws: XLSX.WorkSheet,
  widths: { wch: number }[]
) => {
  ws["!cols"] = widths;
};

const setRowHeightsLocal = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

// --- STYLING CONSTANTS (self-contained) ---

const TEMPLATE_CELL_STYLES = {
  sheetTitle: {
    font: { bold: true, size: 16, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "2563EB" } },
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  },
  sheetSubtitle: {
    font: { bold: false, size: 12, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F3F4F6" } },
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  },
  tableHeader: {
    font: { bold: true, size: 11, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "059669" } },
    alignment: { horizontal: "center", vertical: "center", wrapText: true },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  },
  sampleDataEven: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "FFFFFF" } },
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "D1D5DB" } },
      bottom: { style: "thin", color: { rgb: "D1D5DB" } },
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
  sampleDataOdd: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F9FAFB" } },
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "D1D5DB" } },
      bottom: { style: "thin", color: { rgb: "D1D5DB" } },
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
};

// --- COLUMN CONFIGURATION ---
const getImportTemplateColumnConfig = (): ColumnConfig[] => [
  { key: "name", label: "Nama Produk*", type: "text", width: 25 },
  { key: "category", label: "Kategori", type: "text", width: 15 },
  { key: "unit", label: "Satuan", type: "text", width: 10 },
  { key: "purchasePrice", label: "Harga Beli", type: "currency", width: 12 },
  { key: "salePrice", label: "Harga Jual*", type: "currency", width: 12 },
  { key: "initialStock", label: "Stok Awal", type: "number", width: 10 },
  { key: "minimumStock", label: "Stok Minimum", type: "number", width: 12 },
  { key: "sku", label: "Kode Produk", type: "text", width: 12 },
  { key: "barcode", label: "Barcode", type: "text", width: 15 },
  { key: "description", label: "Deskripsi", type: "text", width: 30 },
];

/**
 * Creates a professional import template for products
 */
export const createProductImportTemplate = (): XLSX.WorkBook => {
  console.log("[TEMPLATE] Creating professional product import template");
  const workbook = XLSX.utils.book_new();
  const columns = getImportTemplateColumnConfig();
  const headerRowCount = 4;

  const sheetTitle = "TEMPLATE IMPORT PRODUK";
  const sheetSubtitle = "KivaPOS - Sistem Manajemen Produk";

  const templateData = [
    [sheetTitle],
    [sheetSubtitle],
    [],
    columns.map((col) => col.label),
    // Sample data rows
    [
      "Contoh Produk 1",
      "Elektronik",
      "Buah",
      50000,
      75000,
      100,
      10,
      "PROD001",
      "1234567890123",
      "Deskripsi produk contoh",
    ],
    [
      "Contoh Produk 2",
      "Makanan",
      "Kg",
      25000,
      35000,
      50,
      5,
      "PROD002",
      "",
      "Produk makanan segar",
    ],
  ];

  const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

  const titleRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}1`;
  mergeCellsLocal(templateSheet, titleRange);
  applyCellStyleLocal(templateSheet, "A1", TEMPLATE_CELL_STYLES.sheetTitle);

  const subtitleRange = `A2:${XLSX.utils.encode_col(columns.length - 1)}2`;
  mergeCellsLocal(templateSheet, subtitleRange);
  applyCellStyleLocal(templateSheet, "A2", TEMPLATE_CELL_STYLES.sheetSubtitle);

  columns.forEach((col, index) => {
    const headerCell = XLSX.utils.encode_cell({
      r: headerRowCount - 1,
      c: index,
    });
    applyCellStyleLocal(
      templateSheet,
      headerCell,
      TEMPLATE_CELL_STYLES.tableHeader
    );
  });

  const sampleDataRowCount = 2;
  for (let rowIndex = 0; rowIndex < sampleDataRowCount; rowIndex++) {
    const actualRowIndex = headerRowCount + rowIndex;
    const isEvenRow = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({
        r: actualRowIndex,
        c: colIndex,
      });
      const style = isEvenRow
        ? TEMPLATE_CELL_STYLES.sampleDataEven
        : TEMPLATE_CELL_STYLES.sampleDataOdd;
      applyCellStyleLocal(templateSheet, cellRef, style);
      if (col.type === "currency" && templateSheet[cellRef]) {
        if (!templateSheet[cellRef].s) templateSheet[cellRef].s = { ...style };
        templateSheet[cellRef].s.numFmt = '"Rp "#,##0_);("Rp "#,##0)';
      }
    });
  }

  const colWidths = columns.map((col) => ({ wch: col.width || 20 }));
  setColumnWidthsLocal(templateSheet, colWidths);
  setRowHeightsLocal(templateSheet, { 1: 25, 2: 20, 3: 15, 4: 35 });

  XLSX.utils.book_append_sheet(workbook, templateSheet, "Template Produk");

  const instructionsData = [
    ["PETUNJUK PENGGUNAAN TEMPLATE IMPORT PRODUK"],
    ["KivaPOS - Panduan Lengkap Import Data Produk"],
    [],
    ["1. KOLOM WAJIB (Ditandai dengan *):"],
    ["   • Nama Produk: Nama unik untuk produk Anda."],
    ["   • Harga Jual: Harga jual kepada pelanggan (angka, tanpa titik/koma)."],
    [],
    ["2. KOLOM OPSIONAL:"],
    ["   • Kategori/Satuan: Akan dibuat otomatis jika belum ada."],
    ["   • Harga Beli: Harga modal produk."],
    ["   • Stok: Jumlah stok awal dan minimum."],
    [],
    ["3. TIPS IMPORT:"],
    ["   • Hapus baris contoh sebelum mengupload data Anda."],
  ];

  const instructionsSheet = XLSX.utils.aoa_to_sheet(instructionsData);
  applyCellStyleLocal(instructionsSheet, "A1", TEMPLATE_CELL_STYLES.sheetTitle);
  applyCellStyleLocal(
    instructionsSheet,
    "A2",
    TEMPLATE_CELL_STYLES.sheetSubtitle
  );
  mergeCellsLocal(instructionsSheet, `A1:${XLSX.utils.encode_col(0)}1`);
  mergeCellsLocal(instructionsSheet, `A2:${XLSX.utils.encode_col(0)}2`);
  setColumnWidthsLocal(instructionsSheet, [{ wch: 120 }]);
  setRowHeightsLocal(instructionsSheet, { 1: 25, 2: 20 });
  XLSX.utils.book_append_sheet(
    workbook,
    instructionsSheet,
    "Petunjuk Penggunaan"
  );

  console.log("[TEMPLATE] Product import template created successfully");
  return workbook;
};
