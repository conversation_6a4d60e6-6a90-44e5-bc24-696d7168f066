// Contact import template generation utilities
// Creates standardized Excel import templates for customers and suppliers

import * as XLSX from "xlsx-js-style";

// --- INTERFACES (self-contained) ---
interface ColumnConfig {
  key: string;
  label: string;
  type: string;
  width?: number; // Custom width in characters
}

// --- UTILITY FUNCTIONS (self-contained) ---

const applyCellStyleLocal = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCellsLocal = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidthsLocal = (
  ws: XLSX.WorkSheet,
  widths: { wch: number }[]
) => {
  ws["!cols"] = widths;
};

const setRowHeightsLocal = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

// --- STYLING CONSTANTS (self-contained) ---

const TEMPLATE_CELL_STYLES = {
  sheetTitle: {
    font: { bold: true, size: 16, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "2563EB" } },
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  },
  sheetSubtitle: {
    font: { bold: false, size: 12, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F3F4F6" } },
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  },
  tableHeader: {
    font: { bold: true, size: 11, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "059669" } },
    alignment: { horizontal: "center", vertical: "center", wrapText: true },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  },
  sampleDataEven: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "FFFFFF" } },
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "D1D5DB" } },
      bottom: { style: "thin", color: { rgb: "D1D5DB" } },
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
  sampleDataOdd: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F9FAFB" } },
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "D1D5DB" } },
      bottom: { style: "thin", color: { rgb: "D1D5DB" } },
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
};

// --- COLUMN CONFIGURATION ---
const getContactColumnConfig = (
  contactType: "Pelanggan" | "Supplier"
): ColumnConfig[] => [
  { key: "name", label: `Nama ${contactType}*`, type: "text", width: 20 },
  { key: "firstName", label: "Nama Depan", type: "text", width: 15 },
  { key: "lastName", label: "Nama Belakang", type: "text", width: 15 },
  { key: "phone", label: "Telepon", type: "text", width: 15 },
  { key: "email", label: "Email", type: "text", width: 25 },
  { key: "companyName", label: "Nama Perusahaan", type: "text", width: 25 },
  { key: "address", label: "Alamat", type: "text", width: 35 },
  { key: "notes", label: "Catatan", type: "text", width: 20 },
];

// --- GENERIC TEMPLATE CREATION LOGIC ---
const createContactTemplate = (
  contactType: "Pelanggan" | "Supplier"
): XLSX.WorkBook => {
  console.log(
    `[TEMPLATE] Creating professional ${contactType.toLowerCase()} import template`
  );
  const workbook = XLSX.utils.book_new();
  const columns = getContactColumnConfig(contactType);
  const headerRowCount = 4;

  const sheetTitle = `TEMPLATE IMPORT ${contactType.toUpperCase()}`;
  const sheetSubtitle = `KivaPOS - Sistem Manajemen ${contactType}`;

  const sampleData =
    contactType === "Pelanggan"
      ? [
          [
            "John Doe",
            "John",
            "Doe",
            "081234567890",
            "<EMAIL>",
            "PT Example Corp",
            "Jl. Contoh No. 123, Jakarta",
            "Pelanggan VIP",
          ],
          [
            "Jane Smith",
            "Jane",
            "Smith",
            "081234567891",
            "<EMAIL>",
            "",
            "Jl. Sample No. 456, Bandung",
            "",
          ],
        ]
      : [
          [
            "PT Supplier ABC",
            "Ahmad",
            "Wijaya",
            "021-12345678",
            "<EMAIL>",
            "PT Supplier ABC",
            "Jl. Industri No. 789, Jakarta",
            "Supplier utama",
          ],
          [
            "CV Supplier XYZ",
            "Siti",
            "Rahayu",
            "021-87654321",
            "<EMAIL>",
            "CV Supplier XYZ",
            "Jl. Perdagangan No. 321, Surabaya",
            "",
          ],
        ];

  const templateData = [
    [sheetTitle],
    [sheetSubtitle],
    [],
    columns.map((col) => col.label),
    ...sampleData,
  ];

  const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

  const titleRange = `A1:${XLSX.utils.encode_col(columns.length - 1)}1`;
  mergeCellsLocal(templateSheet, titleRange);
  applyCellStyleLocal(templateSheet, "A1", TEMPLATE_CELL_STYLES.sheetTitle);

  const subtitleRange = `A2:${XLSX.utils.encode_col(columns.length - 1)}2`;
  mergeCellsLocal(templateSheet, subtitleRange);
  applyCellStyleLocal(templateSheet, "A2", TEMPLATE_CELL_STYLES.sheetSubtitle);

  columns.forEach((col, index) => {
    const headerCell = XLSX.utils.encode_cell({
      r: headerRowCount - 1,
      c: index,
    });
    applyCellStyleLocal(
      templateSheet,
      headerCell,
      TEMPLATE_CELL_STYLES.tableHeader
    );
  });

  const sampleDataRowCount = 2;
  for (let rowIndex = 0; rowIndex < sampleDataRowCount; rowIndex++) {
    const actualRowIndex = headerRowCount + rowIndex;
    const isEvenRow = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({
        r: actualRowIndex,
        c: colIndex,
      });
      const style = isEvenRow
        ? TEMPLATE_CELL_STYLES.sampleDataEven
        : TEMPLATE_CELL_STYLES.sampleDataOdd;
      applyCellStyleLocal(templateSheet, cellRef, style);
    });
  }

  const colWidths = columns.map((col) => ({ wch: col.width || 20 }));
  setColumnWidthsLocal(templateSheet, colWidths);
  setRowHeightsLocal(templateSheet, { 1: 25, 2: 20, 3: 15, 4: 35 });

  XLSX.utils.book_append_sheet(
    workbook,
    templateSheet,
    `Template ${contactType}`
  );

  const instructionsData = [
    [`PETUNJUK PENGGUNAAN TEMPLATE IMPORT ${contactType.toUpperCase()}`],
    [`KivaPOS - Panduan Lengkap Import Data ${contactType}`],
    [],
    ["1. KOLOM WAJIB (Harus diisi):"],
    [`   • Nama ${contactType}: Nama lengkap atau nama perusahaan.`],
    [],
    ["2. TIPS IMPORT:"],
    ["   • Hapus baris contoh sebelum mengupload data Anda."],
    ["   • Pastikan tidak ada baris kosong di tengah data."],
  ];

  const instructionsSheet = XLSX.utils.aoa_to_sheet(instructionsData);
  applyCellStyleLocal(instructionsSheet, "A1", TEMPLATE_CELL_STYLES.sheetTitle);
  applyCellStyleLocal(
    instructionsSheet,
    "A2",
    TEMPLATE_CELL_STYLES.sheetSubtitle
  );
  mergeCellsLocal(instructionsSheet, `A1:${XLSX.utils.encode_col(0)}1`);
  mergeCellsLocal(instructionsSheet, `A2:${XLSX.utils.encode_col(0)}2`);
  setColumnWidthsLocal(instructionsSheet, [{ wch: 120 }]);
  setRowHeightsLocal(instructionsSheet, { 1: 25, 2: 20 });
  XLSX.utils.book_append_sheet(
    workbook,
    instructionsSheet,
    "Petunjuk Penggunaan"
  );

  console.log(`[TEMPLATE] ${contactType} import template created successfully`);
  return workbook;
};

/**
 * Creates a professional import template for customers
 */
export const createCustomerImportTemplate = (): XLSX.WorkBook => {
  return createContactTemplate("Pelanggan");
};

/**
 * Creates a professional import template for suppliers
 */
export const createSupplierImportTemplate = (): XLSX.WorkBook => {
  return createContactTemplate("Supplier");
};
