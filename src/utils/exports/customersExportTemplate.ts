// Professional Excel export functionality for Customers, with customizable column widths and self-contained styles.

import * as XLSX from "xlsx-js-style";

// --- INTERFACES ---

interface ColumnConfig {
  key: string;
  label: string;
  type: string;
  width?: number;
  formatter?: (value: any) => any;
}

interface ExportOptions {
  companyName?: string;
  reportTitle?: string;
  customColumnWidths?: { [columnKey: string]: number };
  useAutoWidth?: boolean;
}

// --- STYLING CONSTANTS ---

const TEMPLATE_CELL_STYLES = {
  sheetTitle: {
    font: { bold: true, size: 16, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "6D28D9" } }, // Purple for Customers
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } },
    },
  },
  sheetSubtitle: {
    font: { bold: false, size: 12, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F3F4F6" } },
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } },
    },
  },
  tableHeader: {
    font: { bold: true, size: 11, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "5B21B6" } }, // Darker Purple
    alignment: { horizontal: "center", vertical: "center", wrapText: true },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } },
    },
  },
  tableDataEven: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "FFFFFF" } },
    alignment: { horizontal: "left", vertical: "top", wrapText: true },
    border: {
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
      top: { style: "thin", color: { rgb: "D1D5DB" } },
      bottom: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
  tableDataOdd: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F9FAFB" } },
    alignment: { horizontal: "left", vertical: "top", wrapText: true },
    border: {
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
      top: { style: "thin", color: { rgb: "D1D5DB" } },
      bottom: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
};

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- COLUMN WIDTH CALCULATION ---

const calculateColumnWidths = (
  columns: ColumnConfig[],
  data: any[],
  options: ExportOptions = {}
): { wch: number }[] => {
  return columns.map((col) => {
    if (options.customColumnWidths && options.customColumnWidths[col.key]) {
      return { wch: options.customColumnWidths[col.key] };
    }
    if (col.width) {
      return { wch: col.width };
    }
    if (options.useAutoWidth) {
      const headerLength = col.label.length;
      const maxDataLength =
        data.length > 0
          ? Math.max(
              ...data.map((row) => {
                const value = getNestedValue(row, col.key);
                return String(value || "").length;
              })
            )
          : 0;
      return {
        wch: Math.min(60, Math.max(headerLength + 5, maxDataLength + 3, 18)),
      };
    }
    return { wch: 20 };
  });
};

// --- COLUMN CONFIGURATION ---

const getCustomersColumnConfig = (): ColumnConfig[] => [
  { key: "name", label: "Nama Pelanggan", type: "text", width: 25 },
  { key: "firstName", label: "Nama Depan", type: "text", width: 20 },
  { key: "lastName", label: "Nama Belakang", type: "text", width: 20 },
  { key: "phone", label: "Telepon", type: "text", width: 18 },
  { key: "email", label: "Email", type: "text", width: 25 },
  { key: "address", label: "Alamat", type: "text", width: 40 },
  { key: "billingAddress", label: "Alamat Penagihan", type: "text", width: 40 },
  {
    key: "shippingAddress",
    label: "Alamat Pengiriman",
    type: "text",
    width: 40,
  },
  { key: "companyName", label: "Nama Perusahaan", type: "text", width: 25 },
  {
    key: "totalTransactions",
    label: "Total Transaksi",
    type: "number",
    width: 15,
  },
  { key: "totalSpent", label: "Total Pembelian", type: "currency", width: 20 },
  { key: "createdAt", label: "Dibuat Pada", type: "date", width: 18 },
];

// --- CUSTOMERS EXPORT ---

const createCustomersDataSheet = (
  data: any[],
  reportPeriod: string,
  options: ExportOptions = {}
): XLSX.WorkSheet => {
  const sheetTitle = "Data Pelanggan";
  const headerRowCount = 4;
  const columns = getCustomersColumnConfig();
  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", TEMPLATE_CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", TEMPLATE_CELL_STYLES.sheetSubtitle);

  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      TEMPLATE_CELL_STYLES.tableHeader
    );
  });

  const rows = data.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (value === null || value === undefined) return "";
      if (col.formatter) value = col.formatter(value);
      switch (col.type) {
        case "currency":
        case "number":
          return typeof value === "number" ? value : 0;
        case "boolean":
          return value ? "Ya" : "Tidak";
        case "date":
          return value ? new Date(value) : "";
        default:
          return String(value);
      }
    })
  );

  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  rows.forEach((_, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    const isEven = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style = JSON.parse(
        JSON.stringify(
          isEven
            ? TEMPLATE_CELL_STYLES.tableDataEven
            : TEMPLATE_CELL_STYLES.tableDataOdd
        )
      );
      if (col.type === "currency") {
        style.numFmt = '"Rp "#,##0_);("Rp "#,##0)';
        style.alignment.horizontal = "right";
      } else if (col.type === "number") {
        style.numFmt = "#,##0";
        style.alignment.horizontal = "right";
      }
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  const colWidths = calculateColumnWidths(columns, data, options);
  setColumnWidths(worksheet, colWidths);

  const rowHeights: { [key: number]: number } = {
    1: 25,
    2: 20,
    [headerRowCount]: 35,
  };
  rows.forEach((_, i) => {
    rowHeights[headerRowCount + i + 1] = 45; // Set a default height for data rows
  });
  setRowHeights(worksheet, rowHeights);

  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (data.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${data.length + headerRowCount}`
    );
  }

  return worksheet;
};

export const createCustomersExcelReport = (
  customersData: any[],
  reportPeriod: string,
  options: ExportOptions = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  const customersSheet = createCustomersDataSheet(
    customersData,
    reportPeriod,
    options
  );
  XLSX.utils.book_append_sheet(workbook, customersSheet, "👥 Pelanggan");
  return workbook;
};
