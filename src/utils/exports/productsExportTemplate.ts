// Professional Excel export functionality for Products, with customizable column widths and self-contained styles.

import * as XLSX from "xlsx-js-style";

// --- INTERFACES ---

interface ColumnConfig {
  key: string;
  label: string;
  type: string;
  width?: number; // Custom width in characters
  formatter?: (value: any) => any;
}

interface ExportOptions {
  companyName?: string;
  reportTitle?: string;
  customColumnWidths?: { [columnKey: string]: number }; // Map column keys to widths
  useAutoWidth?: boolean; // Default: false (use manual widths)
}

// --- STYLING CONSTANTS ---

const TEMPLATE_CELL_STYLES = {
  sheetTitle: {
    font: { bold: true, size: 16, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "059669" } }, // Green for Products
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } },
    },
  },
  sheetSubtitle: {
    font: { bold: false, size: 12, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F3F4F6" } },
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } },
    },
  },
  tableHeader: {
    font: { bold: true, size: 11, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "047857" } }, // Darker Green
    alignment: { horizontal: "center", vertical: "center", wrapText: true },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } },
    },
  },
  tableDataEven: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "FFFFFF" } },
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
  tableDataOdd: {
    font: { size: 10, color: { rgb: "374151" } },
    fill: { fgColor: { rgb: "F9FAFB" } },
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      left: { style: "thin", color: { rgb: "D1D5DB" } },
      right: { style: "thin", color: { rgb: "D1D5DB" } },
    },
  },
};

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- COLUMN WIDTH CALCULATION ---

const calculateColumnWidths = (
  columns: ColumnConfig[],
  data: any[],
  options: ExportOptions = {}
): { wch: number }[] => {
  return columns.map((col) => {
    if (options.customColumnWidths && options.customColumnWidths[col.key]) {
      return { wch: options.customColumnWidths[col.key] };
    }
    if (col.width) {
      return { wch: col.width };
    }
    if (options.useAutoWidth) {
      const headerLength = col.label.length;
      const maxDataLength =
        data.length > 0
          ? Math.max(
              ...data.map((row) => {
                const value = getNestedValue(row, col.key);
                return String(value || "").length;
              })
            )
          : 0;
      return {
        wch: Math.min(60, Math.max(headerLength + 5, maxDataLength + 3, 18)),
      };
    }
    return { wch: 20 };
  });
};

// --- PREDEFINED COLUMN CONFIGURATIONS ---

const getProductsColumnConfig = (): ColumnConfig[] => [
  { key: "name", label: "Nama Produk", type: "text", width: 30 },
  { key: "description", label: "Deskripsi", type: "text", width: 40 },
  { key: "sku", label: "SKU Induk", type: "text", width: 15 },
  { key: "category.name", label: "Kategori", type: "text", width: 20 },
  { key: "unit", label: "Unit", type: "text", width: 10 },
  { key: "price", label: "Harga Jual Induk", type: "currency", width: 18 },
  { key: "cost", label: "Harga Beli Induk", type: "currency", width: 18 },
  { key: "stock", label: "Total Stok", type: "number", width: 12 },
  {
    key: "isDraft",
    label: "Status",
    type: "text",
    width: 12,
    formatter: (isDraft: boolean) => (isDraft ? "Draft" : "Selesai"),
  },
  { key: "variant.colorName", label: "Nama Varian", type: "text", width: 20 },
  { key: "variant.sku", label: "SKU Varian", type: "text", width: 15 },
  { key: "variant.price", label: "Harga Varian", type: "currency", width: 18 },
  { key: "variant.stock", label: "Stok Varian", type: "number", width: 12 },
];

// --- MERGE LOGIC ---
const applyMergesAndVerticalAlign = (
  worksheet: XLSX.WorkSheet,
  data: any[],
  columns: ColumnConfig[],
  headerRowCount: number
) => {
  const mergeableColumns = columns
    .map((col, index) => ({ ...col, index }))
    .filter((col) => !col.key.startsWith("variant."));

  if (data.length === 0) return;

  data.forEach((_, rowIndex) => {
    mergeableColumns.forEach((col) => {
      const cellRef = XLSX.utils.encode_cell({
        r: rowIndex + headerRowCount,
        c: col.index,
      });
      if (worksheet[cellRef]) {
        if (!worksheet[cellRef].s) worksheet[cellRef].s = {};
        if (!worksheet[cellRef].s.alignment)
          worksheet[cellRef].s.alignment = {};
        worksheet[cellRef].s.alignment.vertical = "center";
      }
    });
  });

  let mergeStartRow = 0;
  for (let i = 1; i <= data.length; i++) {
    if (i === data.length || data[i].id !== data[i - 1].id) {
      if (i - mergeStartRow > 1) {
        mergeableColumns.forEach((col) => {
          const start = { r: mergeStartRow + headerRowCount, c: col.index };
          const end = { r: i - 1 + headerRowCount, c: col.index };
          if (!worksheet["!merges"]) worksheet["!merges"] = [];
          worksheet["!merges"].push({ s: start, e: end });
        });
      }
      mergeStartRow = i;
    }
  }
};

// --- PRODUCTS EXPORT ---

const createCombinedProductsSheet = (
  productsData: any[],
  reportPeriod: string,
  options: ExportOptions = {}
): XLSX.WorkSheet => {
  const sheetTitle = "Laporan Rinci Produk";
  const headerRowCount = 4;
  const columns = getProductsColumnConfig();

  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  const processedData = productsData.flatMap((product) => {
    if (product.variants && product.variants.length > 0) {
      return product.variants.map((variant: any) => ({
        ...product,
        variant: variant,
      }));
    }
    return [{ ...product, variant: {} }];
  });

  processedData.forEach((row, index, arr) => {
    row.isFirstInGroup = index === 0 || row.id !== arr[index - 1].id;
    row.isLastInGroup =
      index === arr.length - 1 || row.id !== arr[index + 1].id;
  });

  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", TEMPLATE_CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", TEMPLATE_CELL_STYLES.sheetSubtitle);

  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  columns.forEach((col, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index });
    applyCellStyle(worksheet, cellRef, TEMPLATE_CELL_STYLES.tableHeader);
  });

  const rows = processedData.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (value === null || value === undefined) return "";
      if (col.formatter) value = col.formatter(value);
      switch (col.type) {
        case "currency":
        case "number":
          return typeof value === "number" ? value : 0;
        case "date":
          return value ? new Date(value) : "";
        default:
          return String(value);
      }
    })
  );
  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  processedData.forEach((item, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style = JSON.parse(
        JSON.stringify(
          rowIndex % 2 === 0
            ? TEMPLATE_CELL_STYLES.tableDataEven
            : TEMPLATE_CELL_STYLES.tableDataOdd
        )
      );

      if (col.type === "currency") {
        style.numFmt = '"Rp "#,##0_);("Rp "#,##0)';
      }

      const border = { style: "thin", color: { rgb: "888888" } };
      if (item.isFirstInGroup) style.border = { ...style.border, top: border };
      if (item.isLastInGroup)
        style.border = { ...style.border, bottom: border };
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  applyMergesAndVerticalAlign(
    worksheet,
    processedData,
    columns,
    headerRowCount
  );

  const colWidths = calculateColumnWidths(columns, processedData, options);
  setColumnWidths(worksheet, colWidths);
  setRowHeights(worksheet, { 1: 25, 2: 20, [headerRowCount]: 35 });
  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (processedData.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${processedData.length + headerRowCount}`
    );
  }

  return worksheet;
};

export const createProductsExcelReport = (
  productsData: any[],
  reportPeriod: string,
  options: ExportOptions = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();
  const combinedSheet = createCombinedProductsSheet(
    productsData,
    reportPeriod,
    options
  );
  XLSX.utils.book_append_sheet(workbook, combinedSheet, "Laporan Rinci Produk");
  return workbook;
};
